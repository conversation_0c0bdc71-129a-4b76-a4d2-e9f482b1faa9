<template>
  <div style="padding: 0 20px;">
    <!-- <ViewTips :tipValue="tipValue" /> -->
    <div class="db-content">
      <div class="represen">
        <a-form-model
          layout="vertical"
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          :label-col="{ span: 24 }"
          :wrapper-col="{ span: 24 }">
            <h3 class="db-title">代表基本信息</h3>
            <div class="db-form">
              <a-row>
                  <a-col :md="24" :lg="24" :xl="20">
                    <a-row type="flex">
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="层级"
                                          prop="jcNr"
                                          class="jiec">
                          <a-input v-model="dbdh.dhmc" disabled></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="届次"
                                          prop="jcDm"
                                          class="jiec">
                          <a-select v-model="ruleForm.jcDm">
                            <a-select-option v-for="item in levelOptions"
                                            :key="item.jcDm"
                                            :label="item.levelName"
                                            :value="item.jcDm">{{ item.levelName }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="所属代表团"
                                          class="jiec">
                          <a-input v-model="orgName" disabled></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="姓名"
                                          prop="userName">
                          <a-input v-model="ruleForm.userName"
                                  :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item prop="xbDm"
                                          label="性别">
                          <a-select v-model="ruleForm.xbDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择">
                            <a-select-option v-for="(item, index) in sex"
                                            :value="item.xbDm"
                                            :key="index">{{ item.sex }}
                            </a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="证件类型"
                                           :rules="[
                                            { required: this.ruleForm.sfzDm, message: '请选择证件类型' },
                                            { trigger: 'blur' },
                                          ]"
                                           prop="zjlxDm">
                          <a-select v-model="ruleForm.zjlxDm"
                                    :disabled="oper == constat.VIEW"
                                    placeholder="请选择"
                                    @change="changezjlxDm"
                                    style="width: 100%;">
                            <a-select-option v-for="(item, index) in cardType"
                                             :value="item.zjlxDm"
                                             :key="index">{{
                                item.zjlxmc
                              }}</a-select-option>
                          </a-select>
                        </a-form-model-item>
                      </a-col>
                      <a-col class="span-class" :md="24" :lg="12" :xl="8">
                        <a-form-model-item label="证件号" prop="sfzDm">
                          <a-input v-model="ruleForm.sfzDm"
                                   :disabled="oper == constat.VIEW"></a-input>
                        </a-form-model-item>
                      </a-col>
                        <a-col class="span-class" :span="24">
                          <a-form-model-item
                            label="单位及职务"
                            prop="workUnit"
                            class="unit_style">
                            <a-input v-model="ruleForm.workUnit"
                                    :disabled="oper == constat.VIEW"></a-input>
                          </a-form-model-item>
                        </a-col>
                      <a-col class="span-class" :span="4">

                      </a-col>
                    </a-row>
                  </a-col>
                  <a-col :class="handleResize()" :md="24" :lg="12" :xl="4">
                  <!-- <a-col :class="computedClasses" :md="24" :lg="12" :xl="4"> -->
                    <a-form-model-item
                      label="代表照片"
                      prop=""
                      :label-col="{ span: 24 }"
                      :wrapper-col="{ span: 24 }"
                      style="line-height: 38px;">
                      <div class="userImage">
                        <a-upload v-model="ruleForm.imageUrl"
                                  :file-list="isfileList"
                                  list-type="picture-card"
                                  :action="action"
                                  accept="image/*"
                                  :before-upload="beforeAvatarUpload"
                                  @change="handleAvatarSuccess"
                                  @preview="handlePreview">
                          <img v-if="imageUrl" :src="imageUrl" alt="avatar" />
                          <div v-else-if="isfileList.length < 1">
                            <a-icon :type="loading ? 'loading' : 'plus'" />
                            <div class="ant-upload-text">上传头像</div>
                          </div>
                        </a-upload>
                        <a-modal :visible.sync="dialogVisible"
                                :footer="null"
                                @cancel="
                        () => {
                          dialogVisible = false;
                        }
                      ">
                          <img :disabled="oper == constat.VIEW"
                              v-if="imageUrl"
                              :src="imageUrl"
                              alt="avatar" />
                        </a-modal>
                      </div>
                    </a-form-model-item>
                  </a-col>
              </a-row>
            </div>
            <h3 class="db-title">代表联系方式</h3>
            <div class="db-form">
              <a-row type="flex">
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <a-form-model-item label="单位电话"
                                      prop="unitPhone"
                                      :rules="[
                                          { trigger: 'blur', validator: validatenuitPhone, message: '请输入正确的单位电话' },
                                        ]">
                    <a-input v-model="ruleForm.unitPhone"
                             :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <a-form-model-item label="手机"
                                     prop="onephone">
                    <a-input v-model="ruleForm.onephone"
                             :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
                <a-col class="span-class" :md="24" :lg="12" :xl="8">
                  <a-form-model-item label="电子邮件"
                                     prop="email"
                                     :rules="[{ trigger: 'blur', validator: validateemail, message: '请输入正确的电子邮件' }]">
                    <a-input v-model="ruleForm.email"

                             :disabled="oper == constat.VIEW"></a-input>
                  </a-form-model-item>
                </a-col>
              </a-row>
            </div>
            <h3 class="db-title">录入人信息</h3>
            <div class="db-form">
              <a-row>
                  <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <a-form-model-item
                      label="录入人层级"
                      prop="instituLevel"
                      class="person_info"
                    >
                          <a-input
                            class="input_style"
                            v-model="personInfo1"
                            autocomplete="off"
                            placeholder="系统自动生成"
                            disabled
                          ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <a-form-model-item
                      label="录入人机构"
                      prop="instituLevel"
                      class="person_info"
                    >
                    <a-input
                      class="input_style"
                      v-model="personInfo2"
                      autocomplete="off"
                      placeholder="系统自动生成"
                      disabled
                    ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col class="span-class" :md="24" :lg="12" :xl="8">
                    <a-form-model-item
                      label="录入人姓名"
                      prop="instituLevel"
                      class="person_info"
                    >
                      <a-input
                        class="input_style"
                        v-model="personInfo3"
                        autocomplete="off"
                        placeholder="系统自动生成"
                        disabled
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
              </a-row>
            </div>
            <!-- </a-collapse-panel> -->

            <!-- <template v-if="oper == constat.VIEW">
              <div class="title_all">
                <span class="title_icon"></span>
                <a-collapse-panel
                  header="其他"
                  class="title_style"
                />
              </div>
              <a-form-model-item prop="isOverseascn"
                                 :label-col="{ span: 2 }">
                <a-row type="flex">
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isOverseascn"
                                :disabled="oper == constat.VIEW">是否归侨眷属</a-checkbox>
                  </a-col>
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isPeasant"
                                :disabled="oper == constat.VIEW">是否农民工</a-checkbox>
                  </a-col>
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isCivilServant"
                                :disabled="oper == constat.VIEW">是否公务员</a-checkbox>
                  </a-col>
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isReappointment"
                                :disabled="oper == constat.VIEW">是否连任代表</a-checkbox>
                  </a-col>
                </a-row>
                <a-row type="flex">
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isRepresentative"
                                :disabled="oper == constat.VIEW">是否同任两级以上代表
                    </a-checkbox>
                  </a-col>
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isUnitLeader"
                                :disabled="oper == constat.VIEW">是否事业单位负责人</a-checkbox>
                  </a-col>
                  <a-col :md="24" :lg="12" :xl="8"
                         :push="4">
                    <a-checkbox v-model="ruleForm.isPartyBranch"
                                :disabled="oper == constat.VIEW">是否村委会村党支部组成人员
                    </a-checkbox>
                  </a-col>
                </a-row>
              </a-form-model-item>
              <a-form-model-item label="简历"
                                 prop="resume"
                                 :label-col="{ span: 2 }">
                <a-textarea placeholder="简历"
                            :rows="4"
                            v-model="ruleForm.resume"
                            :disabled="oper == constat.VIEW" />
              </a-form-model-item>
              <a-form-model-item label="备注"
                                 prop="remark"
                                 :label-col="{ span: 2 }">
                <a-textarea placeholder="备注"
                            :rows="4"
                            v-model="ruleForm.remark"
                            :disabled="oper == constat.VIEW" />
              </a-form-model-item>
            </template> -->
          <a-row type="flex"
                 justify="center"
                 v-if="oper != constat.VIEW"
                 style="margin-top: 25px;margin-bottom: -70px;">
            <a-form-model-item>
              <a-space>
                <a-button @click="resetForm('ruleForm')"
                          v-show="!isShowInTo">暂存为草稿</a-button>
                <a-button type="primary"
                          @click="submitForm('ruleForm')"
                          v-show="!isShowInTo">保存并送审</a-button>
                <a-button @click="cancel"
                          v-show="!isShowInTo">取消</a-button>
              </a-space>
            </a-form-model-item>
          </a-row>
          <a-row type="flex"
                 justify="center">
            <a-form-model-item>
              <a-space v-if="recordName != 'LookSee'">
                <a-button @click="lczzjl"
                          v-show="isShowInTo">查询进度</a-button>
                <a-button type="primary"
                          @click="handleAudit"
                          v-show="isShowInTo">送审</a-button>
                <a-button @click="cancel"
                          v-show="isShowInTo">取消</a-button>
              </a-space>
              <a-space v-if="recordName == 'LookSee'">
                <a-button @click="lczzjl"
                          v-show="isShowInTo">查询进度</a-button>
                <a-button @click="cancel"
                          v-show="isShowInTo">取消</a-button>
              </a-space>
            </a-form-model-item>
          </a-row>
        </a-form-model>
      </div>
      <submitForCensorship ref="submitForCensorship"
                           :procInstId="procInstId"
                           @complete="handleComplete"
                           :ids="ids">
      </submitForCensorship>
      <lczzjl ref="lczzjl"
              :procInstId="procInstId"></lczzjl>
    </div>
    <!-- </div> -->
  </div>
</template>

<script>
import submitForCensorship from "@/views/common/submitForCensorship.vue";
import lczzjl from '@/views/common/lczzjl.vue';
import Vue from "vue";
import {dbleaveuploadApi,} from "@/api/representativeElection/candidateApi.js";
import {checkIdNumberValid} from "@/utils/validate";
import {
  addRepresentativeComplete,
  checkUsernameAndMobile,
  getAddRepresentativeById,
  getBaseInfo,
  getInfo,
  insertWithWorkFlow,
} from "@/api/dbxx";
import constat from "@/utils/constat";
import moment from 'moment';
import ViewTips from '@/components/ViewTips/index.vue';
import {getDbdhByOrgId} from "@/api/system/dbdh";
import {levelList} from "@/api/area";
import {Modal} from "ant-design-vue";

export default {
  components: { submitForCensorship, lczzjl, ViewTips },
  filters: {},
  data () {
    const checkIdNumberValids = (rule, data, callBack) => {
      if (checkIdNumberValid(data)) {
        callBack();
      } else {
        callBack(new Error("请输入有效身份证件"));
      }
    };
    // 根据证件号校验生日
    var validateDateOfBirth = (rule, value, callback) => {
      if (! value) {
        // callback(new Error("请选择出生日期"));
        callback();
      } else {
        if (this.ruleForm.birthday) {
          let year = this.ruleForm.birthday.slice(6, 10);
          let month = this.ruleForm.birthday.slice(10, 12);
          let day = this.ruleForm.birthday.slice(12, 14);
          // console.log(value);
          // let isYear = value.split("-")[0] == year;
          // let isMonth = value.split("-")[1] == month;
          // let isDay = value.split("-")[2] == day;
          let temp_date = new Date(year, parseFloat(month) - 1, parseFloat(day))
          if (temp_date) {
            callback();
          } else {
            callback(new Error("请选择正确的出生日期"));
          }
        }
      }
    };
    // 根据证件类型校验证件号
    var validateIdNum = (rule, value, callback) => {
      if (!value) {
        // callback(new Error("请输入证件号"));
        callback();
      } else {
        if (this.ruleForm.zjlxDm === "01") {
          let reg = /^[1-9]\d{7}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}$|^[1-9]\d{5}[1-9]\d{3}((0\d)|(1[0-2]))(([0|1|2]\d)|3[0-1])\d{3}([0-9]|X)$/;
          reg.test(value)
            ? callback()
            : callback(new Error("请输入正确的证件号"));
        }
        if (this.ruleForm.zjlxDm === "02") {
          // let reg = /^[\u4E00-\u9FA5](字第)([0-9a-zA-Z]{4,8})(号?)$/;
          // reg.test(value)
          //   ? callback()
          //   : callback(new Error("请输入正确的证件号"));
          callback();
        }
      }
    };
    // 校验手机号
    var validatePhone = (rule, value, callback) => {
      if (value == "") {
        callback();
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        (reg.test(value) || value == '10000000000')
          ? callback()
          : callback(new Error("请输入正确的手机号"));
      }
    };
    // 校验单位电话
    var validatenuitPhone = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^([1][3,4,5,6,7,8,9])\d{9}$/;
        let tel_reg = /^0\d{2,3}-?\d{7,8}$/;
        reg.test(value) || tel_reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位电话"));
      }
    };
    //校验电子邮件
    var validateemail = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的电子邮件"));
      }
    };
    //校验单位邮政编码
    var validatenuitPostalCode = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的单位邮编"));
      }
    };
    //校验家庭邮政编码
    var validatehouseCode = (rule, value, callback) => {
      if (!value) {
        callback();
      } else {
        let reg = /^[1-9]\d{5}$/;
        reg.test(value)
          ? callback()
          : callback(new Error("请输入正确的家庭邮编"));
      }
    };
    return {
      validatehouseCode,
      validatenuitPostalCode,
      validatenuitPhone,
      validateemail,
      validateDateOfBirth,
      // +
      dialogVisible: false,
      isfileList: [],
      imageUrl: "",
      loading: false,
      action: Vue.prototype.GLOBAL.basePath_1 + '/dbxx/',

      isShowInTo: false,
      ismemberId: "",
      procInstId: "",
      ids: [],
      activeKey: ["k1", "k2", "k3", "k4", "k5"],
      customStyle: "background:rgba(190,64,61,0.1);",
      active: 0,
      oper: constat.ADD,
      ruleForm: {
        sfzDm: null,
        housePostalCode: null,
        unitPhone: null,
        unitPostalCode: null,
        email: null,
        birthday: null,
        dbt: null,
      },
      sex: [],
      nationality: [],
      cardType: [],
      treeData: [],
      politicalAffiliation: [],
      education: [],
      degree: [],
      rules: {
        // jcNr: [{ required: true, message: '请选择层级', trigger: 'change' },],
        jcDm: [{ required: true, message: '请选择届次', trigger: 'change' },],
        dbzHm: [{ required: true, message: "请输入代表证号", trigger: "blur" }],
        userName: [{ required: true, message: "请输入姓名", trigger: "blur" }],
        xbDm: [{ required: true, message: "请输入性别", trigger: "change" }],
        // zjlxDm: [{ message: "请输入证件类型", trigger: "change"}],
        sfzDm: [{ trigger: 'blur', validator: validateIdNum, message: '请输入正确的证件号' }],
        workUnit: [{ required: true, message: "请输入工作单位", trigger: "blur" }],
        // unitPhone: [
        //   { required: false, validator: validatenuitPhone, trigger: "blur" },
        // ],
        // email: [{ required: false, trigger: "blur", validator: validateemail }],
        onephone: [
          { required: true, trigger: "blur", message: "请输入手机号" },
          { trigger: "blur", validator: validatePhone },
        ],
      },
      constat,
      sfDm: "",
      orgId: "",
      levelOptions: [],
      hierarchyOptions: ['市本级'],
      isAudit: "",
      recordName: '',
      isNeedJoninTime: true,
      personInfo1: '',
      personInfo2: '',
      personInfo3: '',
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
      dbdh: {},
      dbtOptions: [
        {name: '越秀区'},
        {name: '海珠区'},
        {name: '荔湾区'},
        {name: '天河区'},
        {name: '白云区'},
        {name: '黄埔区'},
        {name: '番禺区'},
        {name: '花都区'},
        {name: '南沙区'},
        {name: '从化区'},
        {name: '增城区'},
      ],
      currentClass: '',
      orgName: "",
      exitInOrgListStr: "",
    };
  },
  mounted () {
    window.addEventListener('resize', this.handleResize);
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.handleResize);
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "代表信息");
    this.$store.dispatch("navigation/breadcrumb2", "管理代表");
    this.$store.dispatch("navigation/breadcrumb3", "新增代表");
    this.oper = this.$route.query.oper;
    this.recordName = this.$route.query.recordName;
    this.orgName = this.$route.query.orgName;

    var organizationId = '';
    if (this.$route.query.organizationId) {
      organizationId = this.$route.query.organizationId
    }
    this.ruleForm.organizationId = organizationId;
    if (this.oper == constat.VIEW || this.oper == constat.EDIT) {
      const id = this.$route.query.id;
      this.ruleForm.jcDm = this.$route.query.jcDm;
      this.isAudit = this.$route.query.isAudit;
      if (this.isAudit == "1") {
        this.isShowInTo = true;
      }
      this.jcDm = this.$route.query.jcDm;
      this.beforeView(id);
    } else if (this.oper == constat.ADD) {
      this.ruleForm.jcDm = this.$route.query.jcDm;
      this.jcDm = this.$route.query.jcDm;
      this.sfDm = this.$route.query.sfDm;
      this.orgId = this.$route.query.orgId;
      this.getBaseInfo();
      this.getDbdhByOrgId(organizationId)
    }
    this.huiXianSons()
  },
  methods: {
    handleResize() {
      let className = '';
      if (window.innerWidth < 991) {
        className = 'span-class'
      } else if (window.innerWidth > 992 && window.innerWidth < 1200) {
        className = 'span-class'
      } else {
        className = ''
      }
      this.currentClass = className
      return this.currentClass
    },
    cleanBirthDay() {
      this.ruleForm.birthday = null
    },
    changezjlxDm () {
      // if (this.ruleForm.zjlxDm === "02") {
      //   this.ruleForm.sfzDm = '0'
      // } else {
      //   this.ruleForm.sfzDm = null
      // }
    },
    // 判断是否为群众或无党派
    changeZzmmDm () {
      if (this.ruleForm.zzmmDm == '12' || this.ruleForm.zzmmDm == '16') {
        this.isNeedJoninTime = false;
      } else {
        this.isNeedJoninTime = true;
      }
    },
    // +
    // 图片预览
    async handlePreview (file) {
      if (!file.url && !file.preview) {
        file.preview = await URL.createObjectURL(file.originFileObj);
      }
      this.imageUrl = file.url || file.preview;
      this.dialogVisible = true;
    },
    //上传头像成功回调方法 @change
    handleAvatarSuccess ({ file, fileList }) {
      this.isfileList = fileList;
      console.log(file, "1223");
      var data = new FormData();
      data.append("file", file);
      dbleaveuploadApi(data).then((res) => {
        this.ruleForm.photograph = res.data.data.relativePath;
        this.imageUrl = this.action + res.data.data.relativePath;
      });
      this.loading = false;
    },
    //上传头像图片格式限制
    beforeAvatarUpload (file, fileList) {
      var reg = /image\/(png|jpg|gif|jpeg|webp)$/;
      const isJPG = reg.test(file.type);
      const isLt8M = file.size / 1024 / 1024 < 8;
      if (!isJPG) {
        this.$message.error("文件格式不正确，请上传图片!");
      }
      if (!isLt8M) {
        this.$message.error("上传头像图片大小不能超过 8MB!");
      }
      if (isJPG && isLt8M) {
        return false;
      } else {
        return true;
      }
    },
    //不能选择今天以后的日期
    disabledDate (current) {
      return current && current > moment().endOf("day");
    },
    //查询进度
    lczzjl () {
      this.$refs.lczzjl.visible = true
      this.$refs.lczzjl.id = this.procInstId
      this.procInstId = this.ruleForm.procInstId;
    },
    //送审
    handleAudit () {
      this.$refs.submitForCensorship.visible = true;
      this.procInstId = this.ruleForm.procInstId;
      this.ids.push(this.ruleForm.id);
    },
    //回显送审
    huiXianSons () {
      if (this.isAudit == "1") {
        // this.procInstId = this.ruleForm.procInstId;
        // this.ids.push(this.ruleForm.id);
      } else {
        this.isShowInTo = false;
      }
    },

    //取消
    cancel () {
      this.isShowInTo = "";
      this.$router.back();
    },
    submitForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
            if (this.oper == constat.EDIT) {
              this.handleUpdate();
            }
            if (this.oper == constat.ADD) {
              this.handleAdd();
            }
        } else {
          console.log("错误!!");
          this.$message.error('请输入完整的信息')
          return false;
        }
      });
    },
    resetForm (formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.ruleForm.sfDm = this.sfDm;
          this.ruleForm.orgId = this.orgId;
          insertWithWorkFlow(this.ruleForm).then((response) => {
            this.$baseMessage("已暂存为草稿", "info");
          });
        } else {
          console.log("错误!!");
          this.$message.error('请输入完整的信息')
          return false;
        }
      });
    },
    getInfo (jcDm, dbId, sfDm) {
      getInfo({ jcDm: jcDm, dbId: dbId, sfDm: sfDm, id: dbId }).then(
        (response) => {
          console.log(response, "jcDm: jcDm, dbId: dbId, sfDm: sfDm, id");
          this.ruleForm = response.data.info;
          this.sex = response.data.sex;
          this.nationality = response.data.nationality;
          this.cardType = response.data.cardType;
          this.politicalAffiliation = response.data.politicalAffiliation;
          this.education = response.data.education;
          this.degree = response.data.degree;
          //

          //
          this.imageUrl = this.action + response.data.info.photograph;
          this.dialogVisible = true;
        }
      );
    },
    // handleUpdate () {
    //   //保存并送审
    //   getpersonalDetailscreateApi(this.ruleForm).then(response => {
    //     if (response.data.code == '0000') {
    //       console.log(response.data, 'response.data');
    //       if (response.data.data == null) {
    //         this.$router.push({
    //           path: '/delegate/manage'
    //         })
    //       }
    //       this.procInstId = response.data.procInstId
    //       this.ids.push(response.data.data.id)
    //       this.$baseMessage("更新成功", "info");
    //       this.$refs.submitForCensorship.visible = true
    //     }
    //   })
    // },
    //审核保存
    handleComplete (data) {
      addRepresentativeComplete(data).then((res) => {
        console.log(res, "新增代表信息记录表新增代表信息记录表");
        if (res.code == "0000") {
          this.$emit("handleClearId");
          this.ids = [];
          this.$refs.submitForCensorship.successComplete();
          this.$message.success(res.msg);
          this.$router.push("/delegate/manageQZJDB");
        } else {
          this.$message.error(res.msg);
        }
      });
    },
    async handleAdd () {
      this.ruleForm.sfDm = this.sfDm;
      this.ruleForm.jcDm = this.jcDm;
      this.ruleForm.orgId = this.orgId;
      var data = this.ruleForm;
      if (this.ruleForm.onephone) {
        data.phone = data.onephone
      }
      if (this.ruleForm.twoPhone) {
        data.phone += ',' + data.twoPhone
      }
      if (this.ruleForm.threePhone) {
        data.phone += ',' + data.threePhone
      }
      // 如果是最小级别机构树，查询代表姓名与手机号是否已存在
      if (data.sfDm === '4' || data.sfDm === '5') {
        await this.findExitInOrderOrg(data);
      } else {
        await this.insertDBBasicInfo(data);
      }
    },

    async findExitInOrderOrg(data) {
      checkUsernameAndMobile({
        orgId: data.orgId,
        username: data.userName,
        mobile: data.onephone,
      }).then((response) => {
        if (response.data.length !== 0) {
          this.exitInOrgListStr = response.data.join('、')
          console.log(this.exitInOrgListStr, 'this.exitInOrgListStr')
          this.showConfirmationModal(data);
        } else {
          // 如果代表姓名与手机号不存在，直接添加代表
          this.insertDBBasicInfo(data);
        }
      });
    },

    showConfirmationModal(data) {
      // 存在返回机构列表，触发提示弹窗“XXX代表已存在XXX街道，请通知XXX街道调整代表团“
      // 您要新增的XX（全国/省/市/区/镇）人大代表XXX（手机号码：XXXXXXXXX）已存在，目前在XXX街联组，请联系XXX街联组管理员先调出
      // const message = `${data.userName}代表已存在${this.exitInOrgListStr}，请通知${this.exitInOrgListStr}调整代表团`;
      console.log(data)
      let sf = "";
      if (data.sfDm === "1") {
        sf = "全国";
      } else if (data.sfDm === "2") {
        sf = "省";
      } else if (data.sfDm === "3") {
        sf = "市";
      } else if (data.sfDm === "4") {
        sf = "区";
      } else if (data.sfDm === "5") {
        sf = "镇";
      }
      const message = `您要新增的${sf}人大代表${data.userName}（手机号码：${data.onephone}）已存在，目前在${this.exitInOrgListStr}，请联系${this.exitInOrgListStr}管理员先调出`;
      Modal.info({
        content: message,
        width: 500,
        // 存在不执行添加代表
        /*onOk: () => {
          this.insertDBBasicInfo(data);
        },
        onCancel: () => {
        },*/
      });
    },

    async insertDBBasicInfo(data) {
      insertWithWorkFlow(data).then((response) => {
        if (response.data == null) {
          this.$router.push({
            path: "/delegate/manageQZJDB",
          });
        }
        this.procInstId = response.data.procInstId;
        this.ids.push(response.data.id);
        this.$baseMessage("新增成功", "info");
        this.$refs.submitForCensorship.visible = true;
      });
    },

    async beforeView (id) {
      await this.getBaseInfo();

      this.getAddRepresentativeById(id);
      // this.findLevelByJcDm();
    },
    getBaseInfo () {
      getBaseInfo({ jcDm: this.jcDm }).then((response) => {
        this.sex = response.data.sex;
        this.nationality = response.data.nationality;
        this.cardType = response.data.cardType;
        this.politicalAffiliation = response.data.politicalAffiliation;
        this.education = response.data.education;
        this.degree = response.data.degree;
      });
    },
    findLevelByJcDm () {
      levelList({ dhDm: this.dbdh.dhDm }).then((response) => {
        this.levelOptions = response.rows;
      });
    },
    async getAddRepresentativeById (id) {
      getAddRepresentativeById({ id: id }).then((res) => {
        this.ruleForm = res.data;
        this.jcDm = res.data.jcDm;
        this.orgId = res.data.orgId;
        this.orgName = res.data.orgName;
        this.getDbdhByOrgId(this.orgId)
        if (res.data.phone.indexOf(',') != '-1') {
          var phonelist = res.data.phone.split(',');
          this.ruleForm.onephone = phonelist[0];
          if (phonelist.length > 1) {
            this.ruleForm.twoPhone = phonelist[1]
          }
          if (phonelist.length > 2) {
            this.ruleForm.threePhone = phonelist[2]
          }
          this.$forceUpdate()
          phonelist = []
          // 加载初始化数据
        } else {
          this.ruleForm.onephone = res.data.phone;
          this.$forceUpdate()
        }
      });
    },
    getDbdhByOrgId(orgId) {
      getDbdhByOrgId({ orgId: orgId }).then((res) => {
        this.dbdh = res.data;
        this.findLevelByJcDm();
      });
    }
  },
};
</script>
<style lang="scss" scoped>
.db-content {
  .db-title {
    margin-top: 20px;
    height: 30px;
    line-height: 30px;
    border-left: 4px solid #c71c33;
    padding-left: 18px;
    font-weight: bold;
    font-size: 18px;
    box-sizing: border-box;
  }
  .db-form {
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    padding: 20px 0;
  }
}
.span-class {
  padding: 0 25px;
}
.table-tips {
  display: flex;
  border: 1px solid #f1f3e1;
  background-color: #fefce7;
  padding: 10px;
  font-size: 14px;
  margin-top: 10px;
  margin-bottom: 5px;
}
.table-container {
  padding-top: 0;
}
.title_all {
   position: relative;

  .title_style {
    padding: 10px 0px 0 10px;
    background: rgba(190, 64, 61, 0.1);
    padding-bottom: 12px;
    border-bottom: 1px solid #cfcece;
  }
  .title_icon {
    position: absolute;
    top: 50%;
    transform: translate(0, -6px);
    display: inline-block;
    width: 6px;
    height: 10px;
    background-color: #d92b3e;
  }
}
::v-deep .ant-calendar-picker {
  width: 100%;
}
::v-deep .ant-upload-list {
  display: none;
  height: 0;
}
::v-deep .ant-upload-list-item {
  height: 0;
}
::v-deep .ant-upload.ant-upload-select-picture-card {
  width: 100%;
  height: 190px;
}

  .db-content {
    margin: 0 auto;
  }

  .userImage {
    height: 200px;
    width: 150px;

    img {
      height: 200px;
      width: 150px;
    }
  }

  .jic {
    position: relative;
  }

  .jicson {
    color: red;
    // font-size: 2px;
    @include add-size($font_size_16);
    position: absolute;
    top: -9px;
    left: -51px;
    z-index: 999;
  }
// }

.userImage {
  height: 200px;
  width: 150px;
}
</style>
