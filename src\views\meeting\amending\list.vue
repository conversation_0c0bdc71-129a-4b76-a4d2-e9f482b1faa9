<template>
  <div class="table-container">
    <ZTable :list="list"
            :columns="columns" />
  </div>
</template>

<script>

export default {
  data () {
    return {
      TBloading: false,
      list: [
        { createTime: '2021.09.21', progressStatus: '正在处理中', commentCategory: '审核数据', generalComments: '广州市第十五届人大常委会第五十一次会议', subject: '人大市委', address: '广州' },
        { createTime: '2021.09.20', progressStatus: '正在处理中', commentCategory: '审核数据', generalComments: '广州市第十五届人大常委会第五十一次会议', subject: '人大市委', address: '广州' },
        { createTime: '2021.09.19', progressStatus: '已办结', commentCategory: '审核数据', generalComments: '广州市第十五届人大常委会第五十一次会议', subject: '人大市委', address: '广州' },
        { createTime: '2021.09.17', progressStatus: '正在处理中', commentCategory: '审核数据', generalComments: '广州市第十五届人大常委会第五十一次会议', subject: '市人大', address: '广州' },
        { createTime: '2021.09.21', progressStatus: '审核中', commentCategory: '审核数据', generalComments: '广州市第十五届人大常委会第五十一次会议', subject: '人大市委', address: '广州' },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "类型",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "commentCategory",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "开始时间",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "办理进度",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "progressStatus",
          customRender: (text, record, index) => {
            return record.progressStatus;
          },
        },

        {
          title: "办理内容",
          align: "center",
          width: 300,
          ellipsis: true,
          dataIndex: "generalComments",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "反映涉及主体",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "subject",
          customRender: (text, record, index) => {
            return text || "/";
          },
        },
        {
          title: "地址",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "address",
          customRender: (text, record, index) => {
            return text;
          },
        },
      ],
    };
  },
  created () {
    this.$store.dispatch("navigation/breadcrumb1", "修改建议管理");
    this.$store.dispatch("navigation/breadcrumb2", "建议建议");
  },
};
</script>
<style scoped>
</style>
