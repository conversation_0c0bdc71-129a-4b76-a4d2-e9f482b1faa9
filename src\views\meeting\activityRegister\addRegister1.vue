<template>
  <div class="register-box table-container">
    <!-- <a-button style="float: left;z-index: 99;" @click="$router.go(-1)"> 返回</a-button> -->
    <div class="Steps">
      <a-steps :current="0">
        <a-step activity-name="登记" />
        <a-step activity-name="初审" />
        <a-step activity-name="终审" />
        <a-step activity-name="活动登记完成" />
      </a-steps>
    </div>

    <a-spin :spinning="iSpinning">
      <a-icon slot="indicator" type="loading" style="font-size: 24px" spin />
      <div class="base-style">
        <div style="font-weight: 600; margin-left: 1%">基本信息</div>
        <div style="border-bottom: 2px solid #e2e2e3; margin-top: 0.5%"></div>
      </div>
      <div class="formData">
          <a-form-model
            ref="ruleForm"
            :model="ruleForm"
            :rules="rules"
            :label-col="{ span: 8 }"
            :wrapper-col="{ span: 16 }"
          >
            <a-row :gutter="30">
              <a-col :span="24">
                <a-row type="flex">
                  <a-col :span="8">
                    <a-form-model-item label="届别" prop="jcDm">
                      <a-select
                        v-model="ruleForm.jcDm"
                        :disabled="showDisabled"
                        placeholder="请选择"
                        allow-clear
                      >
                        <a-select-option
                          v-for="item in periodList"
                          :key="item.jcDm"
                          :value="item.jcDm"
                          >{{ item.levelName }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="textBox + '名称'"
                      prop="activityName"
                    >
                      <a-input
                        v-model="ruleForm.activityName"
                        :disabled="showDisabled"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="textBox + '编号'"
                      prop="activityNo"
                    >
                      <a-input
                        v-model="ruleForm.activityNo"
                        disabled
                        placeholder="系统自动生成"
                        >></a-input
                      >
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="textBox + '类型'"
                      prop="activityTypeDm"
                    >
                      <a-select
                        v-model="ruleForm.activityTypeDm"
                        placeholder="请选择"
                        allow-clear
                      >
                        <a-select-option
                          v-for="item in typeData"
                          :key="item.activityTypeDm"
                          :value="item.activityTypeDm"
                          :disabled="item.isUse == 'N'"
                          :label="item.activityTypeName"
                          >{{ item.activityTypeName }}</a-select-option
                        >
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item :label="textBox + '性质'">
                      <a-select
                        v-model="ruleForm.activityNatureDm"
                        :disabled="showDisabled"
                        placeholder="请选择"
                        allow-clear
                      >
                        <a-select-option
                          v-for="item in natureData"
                          :key="item.activityNatureDm"
                          :value="item.activityNatureDm"
                          :label="item.activityNatureName"
                          >{{ item.activityNatureName }}
                        </a-select-option>
                      </a-select>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item :label="textBox + '地点'" prop="address">
                      <a-input
                        v-model="ruleForm.address"
                        :disabled="showDisabled"
                      ></a-input>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="textBox + '开始时间'"
                      prop="startTime"
                      style="display: flex"
                    >
                      <div style="display: flex; margin-top: 4px">
                        <a-date-picker
                          v-model="ruleForm.startTime"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          :disabled="showDisabled"
                          allow-clear
                          :show-time="chooseActiveType !== 1 ? { defaultValue: moment('09:00', 'HH:mm'),format: 'HH:mm', minuteStep: 15, hourStep: 1 } : false"
                          placeholder="选择开始时间"
                          style="width: 75%"
                        ></a-date-picker>
                        <a-select
                          v-model="ruleForm.startTimeRange"
                          style="width: 25%"
                          :disabled="showDisabled"
                          v-if="chooseActiveType === 1"
                        >
                          <a-select-option key="1" value="1"
                            >上午</a-select-option
                          >
                          <a-select-option key="2" value="2"
                            >下午</a-select-option
                          >
                          <a-select-option v-if="iShow" key="3" value="3"
                            >晚上</a-select-option
                          >
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      style="display: flex"
                      :disabled="showDisabled"
                      :label="textBox + '结束时间'"
                      prop="endTime"
                    >
                      <div style="display: flex; margin-top: 4px">
                        <a-date-picker
                          v-model="ruleForm.endTime"
                          :disabled="showDisabled"
                          allow-clear
                          :showTime="chooseActiveType !== 1 ? { defaultValue: moment('09:00', 'HH:mm'),format: 'HH:mm', minuteStep: 15, hourStep: 1 } : false"
                          value-format="YYYY-MM-DD HH:mm:ss"
                          placeholder="选择结束时间"
                          style="width: 75%"
                        >
                        </a-date-picker>
                        <a-select
                          v-model="ruleForm.endTimeRange"
                          style="width: 25%"
                          :disabled="showDisabled"
                          v-if="chooseActiveType === 1"
                        >
                          <a-select-option key="1" value="1"
                            >上午</a-select-option
                          >
                          <a-select-option key="2" value="2"
                            >下午</a-select-option
                          >
                          <a-select-option v-if="iShow" key="3" value="3"
                            >晚上</a-select-option
                          >
                        </a-select>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <a-col :span="8"></a-col>
                  <!-- <a-col :span="8">
                  <a-form-model-item label="会议开始时间">
                    <a-input-number
                      v-model="ruleForm.startTime2"
                      :min="0"
                      :max="24"
                      :formatter="formatter"
                    />
                  </a-form-model-item>
                </a-col>
                <a-col :span="8">
                  <a-form-model-item label="会议结束时间">
                    <a-input-number
                      v-model="ruleForm.endTime2"
                      :min="0"
                      :max="24"
                      :formatter="formatter"
                    />
                  </a-form-model-item>
                </a-col>-->
                  <a-col :span="8"></a-col>
                  <a-col :span="8"></a-col>
                  <a-col :span="8"></a-col>

                  <a-col :span="16">
                    <!-- <a-form-model-item label="组织单位"
                                      prop="orgName">
                      <div class="searchStyle"
                          >
                        <a-input v-model="ruleForm.orgName"
                                disabled
                                enter-button></a-input>
                        <a-button :disabled="showDisabled"
                                  type="primary"
                                  icon="search"
                                
                                  @click="openUnit"></a-button>
                      </div>
                    </a-form-model-item> -->
                    <a-form-model-item
                      label="组织单位"
                      prop="orgName"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <div class="searchStyle">
                        <a-input v-model="ruleForm.orgName" disabled></a-input>
                        <a-button
                          type="primary"
                          icon="search"
                          @click="handlePublish"
                        ></a-button>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!--  enter-button -->
                  <!-- <a-col :span="8"></a-col> -->

                  <a-col :span="8"></a-col>
                  <a-col :span="16">
                    <a-form-model-item
                      label="同行代表"
                      prop="inviteRangeDesc"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <div class="searchStyle">
                        <a-input
                          v-model="ruleForm.inviteRangeDesc"
                          disabled
                        ></a-input>
                        <a-button
                          :disabled="showDisabled"
                          type="primary"
                          icon="search"
                          @click="openattendMembers"
                        >
                        </a-button>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- enter-button -->
                  <!-- <a-col :span="8"></a-col> -->

                  <a-col :span="16"></a-col>
                  <a-col :span="16">
                    <a-form-model-item
                      label="工作人员"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <div class="searchStyle">
                        <a-input v-model="ruleForm.workerDesc" disabled></a-input>
                        <a-button
                          :disabled="showDisabled"
                          type="primary"
                          icon="search"
                          @click="openJoinTable"
                        ></a-button>
                      </div>
                    </a-form-model-item>
                  </a-col>
                  <!-- <a-col :span="8"></a-col> -->
                  <a-col :span="8"></a-col>
                  <a-col :span="8">
                    <a-form-model-item
                      :label="textBox + '内容'"
                      prop="activityContent"
                    >
                      <div class="textarea">
                        <a-textarea
                          v-model="ruleForm.activityContent"
                          :disabled="showDisabled"
                          rows="4"
                          allow-clear
                        >
                        </a-textarea>
                      </div>
                    </a-form-model-item>
                  </a-col>
                </a-row>
                <a-row>
                  <a-col v-if="editShow" :span="16">
                    <a-form-model-item
                      label="出席情况"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-button type="primary" @click="attendance">
                        应出席{{ figure.allAttendNum }}人,实际出席人员{{
                          figure.realAttendNum
                        }}人,点击查看详情
                      </a-button>
                    </a-form-model-item>
                  </a-col>
                  <a-col v-if="editShow" :span="16">
                    <a-form-model-item
                      v-if="fyqkShow"
                      label="发言情况"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-button type="primary" @click="condition('发言情况')"
                        >发言人数{{ figure.speakerNum }}人,点击查看详情</a-button
                      >
                    </a-form-model-item>
                    <a-form-model-item
                      v-if="fyqkShow"
                      label="议案建议情况"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-button type="primary" @click="proposals('议案建议情况')">
                        产生议案数{{ figure.proposalNum }}件,产生建议数{{
                          figure.suggestNum
                        }}件,点击查看详情
                      </a-button>
                    </a-form-model-item>
                    <a-form-model-item
                      v-if="fyqkShow"
                      label="质询和询问情况"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-button
                        type="primary"
                        @click="explanation('质询和询问情况')"
                      >
                        提出质询案{{ figure.inquiryNum }}件,提出询问案{{
                          figure.askNum
                        }}件,点击查看详情
                      </a-button>
                    </a-form-model-item>
                    <a-form-model-item
                      label="上传书面报告"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-upload
                        :action="action"
                        name="file"
                        :multiple="true"
                        :file-list="fileList"
                        :before-upload="drUpload"
                        @change="statement"
                      >
                        <a-button :disabled="showDisabled" type="primary"
                          >点击上传</a-button
                        >
                      </a-upload>
                    </a-form-model-item>
                    <div
                      v-if="dataSource"
                      class="table"
                      style="width: 128%; margin-left: -26%"
                    >
                      <a-form-model-item label="书面详情">
                        <a-spin
                          :indicator="indicator"
                          :spinning="dataSourceLoading"
                        >
                          <a-table
                            :columns="columns"
                            :data-source="dataSource"
                            row-key="id"
                          ></a-table>
                        </a-spin>
                      </a-form-model-item>
                    </div>

                    <a-form-model-item
                      label="上传附件"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                    >
                      <a-upload
                        :action="action1"
                        name="file"
                        :multiple="true"
                        :file-list="fileList1"
                        :before-upload="drUpload1"
                        @change="statement1"
                      >
                        <a-button :disabled="showDisabled" type="primary"
                          >点击上传</a-button
                        >
                      </a-upload>
                    </a-form-model-item>
                    <div
                      v-if="dataSource1"
                      class="table"
                      style="width: 128%; margin-left: -26%"
                    >
                      <a-form-model-item label="上传附件详情">
                        <a-spin
                          :indicator="indicator"
                          :spinning="dataSource1Loading"
                        >
                          <a-table
                            :columns="columns1"
                            :data-source="dataSource1"
                            row-key="id"
                          ></a-table>
                        </a-spin>
                      </a-form-model-item>
                    </div>
                  </a-col>
                </a-row>
              </a-col>
            </a-row>
          </a-form-model>
          <a-row slot="footer">
            <a-col :span="8" push="10">
              <a-space>
                <a-button
                  v-if="!isAudit"
                  v-show="isTitle != '查看'"
                  class="confirm"
                  @click="checkConflict"
                  >录入详情</a-button
                >
                <!-- <a-button
                  v-if="isshowIn"
                  class="confirm"
                  @click="SubmitCensorship(false, false)"
                  >保存</a-button
                > -->
                <a-button
                  v-show="isshowIn && ruleForm.currStateName != '已终审'"
                  class="confirm"
                  @click="SubmitCensorship(false, true)"
                  >送审</a-button
                >
                <!-- <a-button v-if="isTitle == '送审'" class="confirm" @click="shenhe"
                  >送审</a-button
                > -->
                <a-button class="confirm" v-if="isTitle == '审核'" @click="shenhe">审核</a-button>
                <a-button class="confirm" @click="lczzjl">流程流转记录</a-button>
                <a-button @click="close">取消</a-button>
              </a-space>
            </a-col>
          </a-row>
      </div>
    </a-spin>
    <representativeUnit
      ref="representativeUnit"
      @confirm="confirmRepresentativeUnit"
    ></representativeUnit>

    <invitedTree
      ref="ShowinvitedTree"
      :jc-dm="ruleForm.jcDm"
      :default-select="defaultSelect"
      @confirm="confirminvitedTree"
    ></invitedTree>

    <workerTree ref="ShowworkerTree" @confirm="confirmworkerTree"></workerTree>
    <!-- <adjust-in v-model="adjustInInfo" @onClose="handleAdjustInClose" /> -->
    <conference ref="conference" :show-disabled="showDisabled"></conference>
    <condition
      ref="condition"
      :show-disabled="showDisabled"
      :title-box="titleBox"
      @handleClearId="ifhandleClearId"
    >
    </condition>
    <proposals
      ref="proposals"
      :show-disabled="showDisabled"
      :title-box="titleProposals"
      @handleClearId="ifhandleClearId"
    ></proposals>
    <explanation
      ref="explanation"
      :show-disabled="showDisabled"
      :title-box="titleExplanation"
      @handleClearId="ifhandleClearId"
    ></explanation>
    <!-- 流程弹出页 -->
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="procInstId"
      :ids="ids"
      @complete="handleComplete"
    >
    </submitForCensorship>
    <!-- 流程流转记录 -->
    <lczzjl ref="lczzjl" :proc-inst-id="ruleForm.procInstId"></lczzjl>

    <orgTreeDialog
      ref="orgTreeDialog"
      :root-org-id="unit"
      @confirm="confirmRepresentativeUnit"
    ></orgTreeDialog>
    <activityConflictInfo
      ref="activityConflictInfo"
      @submit="handleSubmit"
    ></activityConflictInfo>


    <a-modal v-model="tipVisible" title="提交提醒" @ok="tipVisible = false">
      <p style="color: #f5222d;
      padding:10px;
      background: #fff1f0;
      border-color: #ffa39e;">该送审由代表在i履职移动端补录，请按照实际情况补充表单缺失项后提交</p>
    </a-modal>
  </div>
</template>

<script>
import submitForCensorship from "@/views/common/submitForCensorship";
import moment from "moment";
import { getdutyActivecompleteApi } from "@/api/xjgw";
import { instance_1 } from "@/api/axiosRq";
import {
  getLevelList,
  getActivityNatureList,
  getActivityTypeData,
} from "@/api/registrationMage/tableIng.js";
import RepresentativeUnit from "@/views/common/representative/RepresenRadio.vue";
import invitedTree from "@/views/common/representative/invitedTree.vue";
import workerTree from "@/views/common/representative/workerTree.vue";
// import adjustIn from "@/views/meeting/delegate/adjustIn";
import conference from "./conference";
import condition from "./condition";
import proposals from "./proposals";
import explanation from "./explanation";
import { myPagination } from "@/mixins/pagination.js";
import lczzjl from "@/views/common/lczzjl";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
import activityConflictInfo from "./activityConflictInfo";

export default {
  components: {
    submitForCensorship,
    RepresentativeUnit,
    // adjustIn,
    invitedTree,
    workerTree,
    conference,
    condition,
    proposals,
    explanation,
    lczzjl,
    orgTreeDialog,
    activityConflictInfo,
  },
  mixins: [myPagination],
  data() {
    return {
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      loading: false,
      isshowIn: false,
      showDisabled: false,
      action: "",
      textBox: "会议",
      action1: "",
      isTitle: "",
      getData: [],
      fileList: [],
      fileList1: [],
      ruleFormData: [],
      adjustInInfo: {
        isShow: true,
      },
      titleBox: "会议",
      tipVisible: false,
      columns: [
        {
          title: "文件名",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.fileName || "/";
          },
        },
        {
          title: "上传日期",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.download(record);
                    },
                  },
                },
                "下载"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deteData(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      columns1: [
        {
          title: "文件名",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.fileName || "/";
          },
        },
        {
          title: "上传日期",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.download1(record);
                    },
                  },
                },
                "下载"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deteData1(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      figure: {
        allAttendNum: null,
        askNum: null,
        inquiryNum: null,
        proposalNum: null,
        realAttendNum: null,
        speakerNum: null,
        suggestNum: null,
      },
      dataSource: null,
      dataSource1: null,
      editShow: false,
      titleBox: "",
      titleProposals: "",
      titleExplanation: "",
      isVisible: false,
      isCondition: false,
      periodList: [] /* 获取届次 */,
      natureData: [],
      typeData: [],
      ruleForm: {
        jcDm: "3" /* 届别 */,
        activityName: "" /* 会议名称 */,
        activityNo: "" /* 会议编号 */,
        activityTypeDm: "" /* 会议类型 */,
        activityTypeName: "" /* 会议类型名称 */,
        activityNatureDm: "" /* 会议性质 */,
        activityNatureName: "" /* 会议性质名称 */,
        address: "" /*  会议地点 */,
        startTime: "" /*  会议开始时间 */,
        endTime: "" /* 会议结束时间   */,
        startTime2: "" /*  会议开始时间 */,
        endTime2: "" /* 会议结束时间   */,
        orgName: "" /* 组织单位 */,
        orgId: "" /* 组织单位 ID*/,
        attendMembers: [] /*  同行代表 */,
        inviteRangeDesc: "" /*  同行代表 字符串*/,
        activityWorkers: [] /* 工作人员 */,
        workerDesc: "" /* 工作人员 字符串*/,
        activityContent: "" /* 会议内容 */,
        endTimeRange: "1" /* 结束时间段 */,
        startTimeRange: "1" /* 开始时间段 */,
      },
      chooseActiveType: 0, // 会议类型判断
      attendMembers: "",
      activityWorkers: "",
      rules: {
        jcDm: [{ required: true, message: "请输入届别", trigger: "blur" }],
        activityName: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
        ],
        // activityNo: [
        //   { required: true, message: "请输入活动编号", trigger: "blur" },
        // ],
        activityTypeDm: [
          { required: true, message: "请选择会议/活动类型", trigger: "blur" },
        ],
        activityNatureDm: [
          { required: true, message: "请输入活动性质", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请输入活动地点", trigger: "blur" },
        ],
        startTime: [
          { required: true, message: "请输入活动开始时间", trigger: "change" },
        ],
        endTime: [
          { required: true, message: "请输入活动结束时间", trigger: "change" },
        ],
        orgName: [
          { required: true, message: "请输入组织单位", trigger: "blur" },
        ],
        attendMembers: [
          { required: true, message: "请输入同行代表", trigger: "blur" },
        ],
        activityWorkers: [
          { required: true, message: "请输入工作人员", trigger: "blur" },
        ],
        inviteRangeDesc: [
          { required: true, message: "请输入同行代表", trigger: "blur" },
        ],
        workerDesc: [
          { required: true, message: "请输入工作人员", trigger: "blur" },
        ],
        activityContent: [
          { required: true, message: "请输入活动内容", trigger: "blur" },
        ],
      },
      thNumOptions: [],
      conferenceData: {},
      dataSourceLoading: false,
      dataSource1Loading: false,
      iSpinning: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      ids: [],
      procInstId: "",
      isAudit: false,
      isTitle: "",
      areaId: "",
      isRecord: {},
      iShow: null,
      defaultSelect: [], //同行代表
      fyqkShow: false,
      SubmitCensorshipShow: true,
      unit: "root",
    };
  },
  created() {
    let { areaId, title, editShow, titleBox, textBox } = this.$route.query;
    console.log(this.$route.query, "this.$route.query");
    this.areaId = areaId;
    this.isAudit = this.$route.query.isAudit;
    if (textBox) {
      this.textBox = textBox;
    }
    if (
      title == "参加市人民代表大会" ||
      title == "修改详情" ||
      title == "查看"
    ) {
      this.iShow = true;
      if (title == "查看") {
        this.showDisabled = true;
      }
    } else {
      this.iShow = false;
    }
    if (this.isAudit) {
      this.iSpinning = true;
      this.loading = true;
      this.editShow = true;
      this.isshowIn = true;
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
    }
    if (editShow) {
      this.iSpinning = true;
      this.loading = true;
      this.editShow = true;
      this.isAudit = true;
      this.isshowIn = true;
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
    }
    if (titleBox == "查看所有") {
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
      this.showDisabled = true;
      this.editShow = true;
    }
    this.action =
      this.GLOBAL.basePath_1 + "/activityReport/upload?activityId=" + areaId;
    this.action1 =
      this.GLOBAL.basePath_1 +
      "/activityAttachment/upload?activityId=" +
      areaId;
    this.$store.dispatch("navigation/breadcrumb1", "登记活动");
    this.$store.dispatch("navigation/breadcrumb2", title);

    // if (title == "修改详情") {
    //   // this.dutyActive(areaId);
    //   this.getLevelListData(title);
    // } else if (title == "送审") {
    //   this.getLevelListData(title);
    //   // this.dutyActive(areaId);
    // } else if (title == "查看") {
    //   this.getLevelListData(title);
    //   // this.dutyActive(areaId);
    // }
    // 判断“发言情况”、“议案建议情况”、“质询案情况”是否显示
    if (
      title == "参加市人民代表大会" ||
      title == "出席市人大常委会会议" ||
      title == "出席市人大常委会主任会议" ||
      title == "列席市人大常委会会议" ||
      title == "列席市人大常委会主任会议" ||
      title == "列席市人大常委会会议并发言"
    ) {
      this.fyqkShow = true;
    } else {
      this.fyqkShow = false;
    }
    if (title == "审核") {
      this.isshowIn = false;
    }
    this.getLevelListData(title);
  },
  methods: {
    disabledDateTime() {
      return {
        disabledSeconds: () => {
          var arr = [];
          var j = 60;
          var i = 1;
          for (j = 60; i < j; i++) {
            arr.push(i);
          }
          return arr;
        },
      };
    },
    // onStartTime(Moment, time) {
    //   // 优化了时间控件，不再需要选择秒
    //   var res = Moment.slice(0, Moment.length - 2);
    //   var hours = Moment.slice(11, Moment.length - 6);
    //   res = res + "00";
    //   this.ruleForm.startTime = res;
    //   if (hours <= 12) {
    //     this.ruleForm.startTimeRange = "1";
    //   } else {
    //     this.ruleForm.startTimeRange = "2";
    //   }
    // },
    // onEndTime(Moment, time) {
    //   console.log(Moment);
    //   // 优化了时间控件，不再需要选择秒
    //   var res = Moment.slice(0, Moment.length - 2);
    //   var hours = Moment.slice(11, Moment.length - 6);
    //   res = res + "00";
    //   this.ruleForm.endTime = res;
    //   if (hours <= 12) {
    //     this.ruleForm.endTimeRange = "1";
    //   } else {
    //     this.ruleForm.endTimeRange = "2";
    //   }
    // },
    ifhandleClearId() {
      let id = this.ruleForm.activityId;
      this.numberPeople(id);
    },
    moment,
    // 导入限制 直接return
    drUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
    },
    // 导入限制 直接return
    drUpload1(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
    },
    numberPeople(areaId) {
      instance_1({
        url: "/dutyActive/countInfo",
        method: "get",
        params: { id: areaId || this.areaId },
      }).then((res) => {
        if (res.data.code == "0000") {
          let {
            allAttendNum,
            askNum,
            inquiryNum,
            proposalNum,
            realAttendNum,
            speakerNum,
            suggestNum,
          } = res.data.data;
          this.figure.allAttendNum = allAttendNum;
          this.figure.askNum = askNum;
          this.figure.inquiryNum = inquiryNum;
          this.figure.proposalNum = proposalNum;
          this.figure.realAttendNum = realAttendNum;
          this.figure.speakerNum = speakerNum;
          this.figure.suggestNum = suggestNum;
          this.iSpinning = false;
        }
      });
    },
    download(record) {
      instance_1({
        url: "/activityReport/download",
        method: "get",
        responseType: "blob",
        params: { id: record.id },
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = record.fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    deteData(record) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确认删除  ？",
        activityContent: "关联数据也将一并删除，你确定要删除选中项吗",
        onOk: () => {
          instance_1({
            url: "/activityReport/deleteById",
            method: "post",
            params: { id: record.id },
          }).then((res) => {
            console.log(res);
            this.fetchData();
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    download1(record) {
      instance_1({
        url: "/activityAttachment/download",
        method: "get",
        responseType: "blob",
        params: { id: record.id },
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = record.fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    deteData1(record) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确认删除  ？",
        activityContent: "关联数据也将一并删除，你确定要删除选中项吗",
        onOk: () => {
          instance_1({
            url: "/activityAttachment/deleteById",
            method: "post",
            params: { id: record.id },
          }).then((res) => {
            console.log(res);
            this.fetchData1();
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //关闭
    close() {
      this.seeTitle = "";
      this.$router.go(-1);
      // this.$router.push({
      //   path: "/registerMange/register",
      //   // 春斌加
      //   query: {
      //     queryForm: this.$route.query.queryForm,
      //     form: this.$route.query.form,
      //   },
      // });
    },
    nationality() {},
    politicalAffiliation() {},
    //  回显
    dutyActive(areaId) {
      instance_1({
        url: "/dutyActive/getById",
        method: "get",
        params: { id: areaId },
      }).then((res) => {
        setTimeout(() => {
          this.loading = false;
        }, 600);
        this.isRecord = res.data.data.attendMembers;
        //获取流程id
        this.isTitle = this.$route.query.title;
        this.ruleForm.procInstId = res.data.data.procInstId;
        this.procInstId = res.data.data.procInstId;
        this.ids.push(res.data.data.id);
        this.conferenceData = res.data.data;
        let {
          jcDm,
          activityName,
          activityNo,
          activityTypeDm,
          activityTypeName,
          activityNatureDm,
          activityNatureName,
          address,
          startTime,
          endTime,
          startTime2,
          endTime2,
          orgName,
          orgId,
          attendMembers,
          inviteRangeDesc,
          activityWorkers,
          workerDesc,
          activityContent,
          currStateName,
          id,
          endTimeRange,
          startTimeRange,
        } = res.data.data;
        this.ruleForm.startTimeRange = startTimeRange /* 开始时间段 */;
        this.ruleForm.endTimeRange = endTimeRange /* 结束时间段 */;
        this.ruleForm.id = id /* 届别 */;
        this.ruleForm.jcDm = jcDm /* 届别 */;
        this.ruleForm.activityName = activityName /* 会议名称 */;
        this.ruleForm.activityNo = activityNo /* 会议编号 */;
        this.ruleForm.activityTypeDm = activityTypeDm /* 会议类型代码 */;
        this.ruleForm.activityTypeName = activityTypeName /* 会议类型名称 */;
        this.ruleForm.activityNatureDm = activityNatureDm /* 会议性质代码 */;
        this.ruleForm.activityNatureName =
          activityNatureName /* 会议性质名称 */;
        this.ruleForm.address = address /*  会议地点 */;
        this.ruleForm.startTime = startTime /*  会议开始时间 */;
        this.ruleForm.endTime = endTime /* 会议结束时间   */;
        this.ruleForm.startTime2 = startTime2 /*  会议开始时间 */;
        this.ruleForm.endTime2 = endTime2 /* 会议结束时间   */;
        this.ruleForm.orgName = orgName /* 组织单位 */;
        this.ruleForm.orgId = orgId /* 组织单位 ID*/;
        this.ruleForm.attendMembers = attendMembers /*  同行代表 */;
        this.ruleForm.inviteRangeDesc = inviteRangeDesc /*  同行代表 字符串*/;
        this.ruleForm.activityWorkers = activityWorkers /* 工作人员 */;
        this.ruleForm.workerDesc = workerDesc /* 工作人员 字符串*/;
        this.ruleForm.activityContent = activityContent; /* 会议内容 */
        this.ruleForm.currStateName = currStateName; /* 会议内容 */
        // 暂做回显
        let checkIdentyData = [];
        let checked = [];
        if (this.ruleForm.attendMembers.length > 0) {
          this.ruleForm.attendMembers.forEach((item) => {
            if (item.postId) {
              checkIdentyData.push("U" + item.postId);
              item.id = "U" + item.postId;
              item.fullName = item.userName;
              checked.push(item);
            } else {
              checkIdentyData = [];
              checked = [];
            }
          });
        }
        this.$refs.ShowinvitedTree.checkIdentyData = checkIdentyData;
        this.$refs.ShowinvitedTree.checked = checked;
        this.$forceUpdate();

        // 判断“发言情况”、“议案建议情况”、“质询案情况”是否显示
        if (
          this.ruleForm.activityTypeName == "参加市人民代表大会" ||
          this.ruleForm.activityTypeName == "出席市人大常委会会议" ||
          this.ruleForm.activityTypeName == "出席市人大常委会主任会议" ||
          this.ruleForm.activityTypeName == "列席市人大常委会会议" ||
          this.ruleForm.activityTypeName == "列席市人大常委会主任会议"
        ) {
          this.fyqkShow = true;
        } else {
          this.fyqkShow = false;
        }
      });
    },
    //审核
    shenhe() {
      // console.log(this.$route.query, 'this.$route.query');
      // 新增一个逻辑，如果是代表i履职填写得数据需要校验一下是否选择了活动类型
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {

          this.ruleForm.attendMembers.forEach((item) => {
            if(!item.userName || item.userName == "") {
              item.userName = item.fullName;
            }
          });

          let url = "/dutyActive/update";
            instance_1({
              url,
              method: "post",
              data: this.ruleForm,
            }).then((res) => {
              if (res.data.code == "0000") {
                this.$refs.submitForCensorship.visible = true;
              } else {
                this.iSpinning = false;
                this.$message.error(res.data.msg);
              }
            });
        } else {
          this.tipVisible = true
        }
      })
    },
    //审核保存发送
    handleComplete(data) {
      this.iSpinning = true
      this.$refs.submitForCensorship.visible = false;
      getdutyActivecompleteApi(data).then((res) => {
        if (res.data.code == "0000") {
          this.$router.push({
            // path: "/registerMange/Handling",
            path: "/registerMange/backlog",
          });
          this.ids = [];
          this.$refs.submitForCensorship.successComplete();
          this.iSpinning = false
          this.$message.success(res.data.msg);
        } else {
          this.iSpinning = false
          this.$message.error(res.data.msg);
        }
      });
    },
    // 上传书面报告按钮
    statement(file) {
      setTimeout(() => {
        this.fetchData();
      }, 1000);
    },
    //  开始回显
    fetchData() {
      this.dataSourceLoading = true;
      instance_1({
        url: "/activityReport/getByActivityId",
        method: "get",
        params: { activityId: this.ruleForm.activityId },
      }).then((res) => {
        this.dataSource = res.data.data;
        this.dataSourceLoading = false;
        this.$refs.conference.selectData(
          this.ruleForm.activityId,
          this.conferenceData,
          this.ruleForm,
          false
        );
        this.$forceUpdate();

        setTimeout(() => {
          this.loading = false;
          this.iSpinning = false;
        }, 1000);
      });
    },
    // 已上传附件
    statement1({ file, fileList }) {
      setTimeout(() => {
        this.fetchData1();
      }, 1000);
    },
    //  开始回显
    fetchData1() {
      this.dataSource1Loading = true;
      instance_1({
        url: "/activityAttachment/getByActivityId",
        method: "get",
        params: { activityId: this.ruleForm.activityId },
      }).then((res) => {
        console.log(res.data);
        this.dataSource1 = res.data.data;
        this.dataSource1Loading = false;
      });
    },
    accessory(file, fileList) {
      console.log(file);
      console.log(fileList);
    },

    //录入详情
    handleSubmit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          if (this.ruleForm.startTime > this.ruleForm.endTime) {
            this.$message.error("当前开始时间晚于结束时间，请您重新输入");
          } else {
            // 加载中
            this.iSpinning = true;
            this.isAudit = true;
            this.ruleForm.attendMembers.forEach((item) => {
              item.userName = item.fullName;
            });
            for (let index = 0; index < this.typeData.length; index++) {
              const element = this.typeData[index];
              if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                this.ruleForm.activityTypeName = element.activityTypeName;
                break;
              }
            }
            for (let index = 0; index < this.natureData.length; index++) {
              const element = this.natureData[index];
              if (element.activityNatureDm == this.ruleForm.activityNatureDm) {
                this.ruleForm.activityNatureName = element.activityNatureName;
                break;
              }
            }
            this.ruleForm.attendMembers.forEach((item) => {
              item.userName = item.fullName;
            });
            // let url = "/dutyActive/create";
            // if (this.editShow) {
            //   url = "/dutyActive/update";
            // }
            let url = "";
            // if (this.editShow) {
            //   url = "/dutyActive/update";
            // } else {
            url = "/dutyActive/create";
            // }
            if (this.chooseActiveType === 0) {
              this.ruleForm.startTimeRange = this.checkDate(this.ruleForm.startTime)
              this.ruleForm.endTimeRange = this.checkDate(this.ruleForm.endTime)
            }
            if (this.chooseActiveType === 1) {
              this.ruleForm.startTime = this.ruleForm.startTime.split(' ')[0].concat(' 00:00:00')
              this.ruleForm.endTime = this.ruleForm.endTime.split(' ')[0].concat(' 00:00:00')
            }
            instance_1({
              url,
              method: "post",
              data: this.ruleForm,
            }).then((res) => {
              if (res.data.code == "0000") {
                this.ruleFormData = res.data.data;
                this.areaId = this.ruleFormData.id;
                this.$message.success("添加成功");
                this.iSpinning = false;
                this.isAudit = true;
                this.isshowIn = true;
                this.editShow = true;
                this.isRecord = res.data.data.attendMembers;
                this.conferenceData = res.data.data;
                this.ruleForm.id = res.data.data.id;
                this.ruleForm.activityId = res.data.data.id;
                this.action =
                  this.GLOBAL.basePath_1 +
                  "/activityReport/upload?activityId=" +
                  this.ruleForm.activityId;
                this.action1 =
                  this.GLOBAL.basePath_1 +
                  "/activityAttachment/upload?activityId=" +
                  this.ruleForm.activityId;
                this.$nextTick(() => {
                  this.numberPeople(this.ruleForm.id);
                  console.log(
                    "🤗🤗🤗, textBox =>",
                    this.ruleForm.activityTypeName
                  );
                  if (this.ruleForm.activityTypeName == "参加市人民代表大会") {
                    this.fyqkShow = true;
                  }
                });
                this.ruleForm.procInstId = res.data.data.procInstId;

                // this.ids.push(res.data.data.id);
                // this.$router.go(-1);
                // this.$refs.submitForCensorship.visible = true;
              } else {
                this.iSpinning = false;
                this.isAudit = false;
                this.$message.error(res.data.msg);
              }
            });
          }
        }
      });
    },
    // 判断时间是上午还是下午
    checkDate(date) {
      const newDate = new Date(date)
      const hour = newDate.getHours();
      if (hour >= 12) {  
        return '2';  
      } else {  
        return '1';  
      } 
    },
    //检查是否有录入冲突
    checkConflict() {
      this.$refs.activityConflictInfo.checkActivityConflict(this.ruleForm);
    },
    //保存并送审
    SubmitCensorship(state, isShow) {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          if (this.ruleForm.startTime > this.ruleForm.endTime) {
            this.$message.error("当前开始时间晚于结束时间，请您重新输入");
          } else {
            this.iSpinning = true;
            // if(this.SubmitCensorshipShow){
            if (
              this.$route.query.title == "修改详情" ||
              this.$route.query.title == "送审" ||
              this.$route.query.title == "审核" ||
              state
            ) {
              this.ruleForm.attendMembers.forEach((item) => {
                item.userName = item.fullName;
              });
              for (let index = 0; index < this.typeData.length; index++) {
                const element = this.typeData[index];
                if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                  this.ruleForm.activityTypeName = element.activityTypeName;
                  break;
                }
              }
              for (let index = 0; index < this.natureData.length; index++) {
                const element = this.natureData[index];
                if (
                  element.activityNatureDm == this.ruleForm.activityNatureDm
                ) {
                  this.ruleForm.activityNatureName = element.activityNatureName;
                  break;
                }
              }
              this.ruleForm.attendMembers.forEach((item) => {
                item.userName = item.fullName;
              });
              this.ruleForm.attendMembers.forEach((item) => {
                item.userName = item.fullName;
              });
              for (let index = 0; index < this.typeData.length; index++) {
                const element = this.typeData[index];
                if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                  this.ruleForm.activityTypeName = element.activityTypeName;
                  break;
                }
              }
              for (let index = 0; index < this.natureData.length; index++) {
                const element = this.natureData[index];
                if (
                  element.activityNatureDm == this.ruleForm.activityNatureDm
                ) {
                  this.ruleForm.activityNatureName = element.activityNatureName;
                  break;
                }
              }
              // this.ruleForm.attendMembers.forEach((item) => {
              //   item.userName = item.fullName;
              // });
              this.ruleFormData = this.ruleForm;
            }

            let url = "/dutyActive/update";
            instance_1({
              url,
              method: "post",
              data: this.ruleFormData,
            }).then((res) => {
              if (res.data.code == "0000") {
                if (isShow) {
                  this.$refs.submitForCensorship.visible = true;
                }
                this.procInstId = res.data.data.procInstId;
                this.ids.push(res.data.data.id);
                let id = res.data.data.id;
                this.numberPeople(id);
              } else {
                this.iSpinning = false;
                this.$message.error(res.data.msg);
              }
            });
          }
        }
        // }
      });
    },
    getLevelListData(title) {
      // 获取会议性质
      let getActivityNatureListData = () => {
        getActivityNatureList().then((res) => {
          if (res.data.code == "0000") {
            this.natureData = res.data.data;
            // 根据id获取回显数据
            // 编辑才回显
            if (this.editShow || title == "查看") {
              this.dutyActive(this.areaId);
            }
          } else {
            this.$message.error(res.data.message);
          }
        });
      };

      // 获取届次
      let getLevelListData = () => {
        getLevelList({}).then((res) => {
          if (res.data.code == "0000") {
            this.periodList = res.data.data;
            // 新增
            if (!this.editShow) {
              this.typeData.forEach((item) => {
                if (item.activityTypeDm == this.$route.query.code) {
                  this.ruleForm.activityTypeDm = item.activityTypeDm;
                }
              });
            }
            getActivityNatureListData();
          } else {
            this.$message.error(res.data.message);
          }
        });
      };

      // 获取会议类型
      getActivityTypeData().then((res) => {
        if (res.data.code == "0000") {
          // 新增
          if (!this.editShow) {
            res.data.data.forEach((item) => {
              if (title == item.activityTypeName) {
                this.ruleForm.activityTypeDm =
                  item.activityTypeDm /* 会议类型代码 */;
              }
            });
          }
          this.typeData = res.data.data;

          getLevelListData();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    //  onendTime(){
    //   this.ruleForm.endTime=''
    //   },
    handleAdjustInClose() {
      this.adjustInInfo.isShow = false;
    },
    openUnit() {
      this.$refs.representativeUnit.orgTreeDialogVisible = true;
      // this.$refs.representativeUnit.initOrgTree('111')
      this.$refs.representativeUnit.orgTreeDialogTitle = "选择组织单位";
    },
    // 机构选择器
    handlePublish() {
      this.$refs.orgTreeDialog.open();
    },
    openattendMembers() {
      this.$refs.ShowinvitedTree.newOrgTreeDialogVisible = true;
      this.$refs.ShowinvitedTree.orgTreeDialogTitle = "选择同行代表";
    },
    openJoinTable() {
      this.$refs.ShowworkerTree.orgTreeDialogVisible = true;
      this.$refs.ShowworkerTree.orgTreeDialogTitle = "选择工作人员";
    },
    confirmRepresentativeUnit(data) {
      if (data) {
        this.ruleForm.orgName = (data[0].parentName ? (data[0].parentName + '-') : '') + data[0].orgName;
        this.ruleForm.orgId = data[0].orgId;
      }
    },
    startTimeData(data, value, DD) {},
    confirminvitedTree(data) {
      if (data) {
        this.defaultSelect = data;
        this.ruleForm.attendMembers = [];
        this.ruleForm.inviteRangeDesc = "";
        this.ruleForm.attendMembers = this.recursionChild(data);
        this.ruleForm.attendMembers.forEach((item, index) => {
          if (index !== this.ruleForm.attendMembers.length - 1) {
            this.ruleForm.inviteRangeDesc =
              this.ruleForm.inviteRangeDesc + item.fullName + "、";
          } else {
            this.ruleForm.inviteRangeDesc =
              this.ruleForm.inviteRangeDesc + item.fullName;
          }
        });
        if (this.isshowIn) {
          //  如果新增后再改变自动修改一下
          this.$nextTick(() => {
            this.SubmitCensorship(true);
          });
        }
      }
    },
    confirmworkerTree(data) {
      if (data) {
        this.ruleForm.activityWorkers = [];
        this.ruleForm.workerDesc = "";
        this.ruleForm.activityWorkers = this.recursionChild(data);
        this.ruleForm.activityWorkers.forEach((item, index) => {
          if (index !== this.ruleForm.activityWorkers.length - 1) {
            this.ruleForm.workerDesc =
              this.ruleForm.workerDesc + item.fullName + "、";
          } else {
            this.ruleForm.workerDesc = this.ruleForm.workerDesc + item.fullName;
          }
        });
      }
    },
    recursionChild(arr) {
      var res = [];
      arr.map((item) => {
        if (item.children && Array.isArray(item.children)) {
          // res = res.concat(this.recursionChild(item.children));
        } else {
          res.push(item);
        }
      });
      return res;
    },
    attendance() {
      this.$refs.conference.selectData(
        this.ruleForm.activityId,
        this.conferenceData,
        this.ruleForm,
        true
      );
    },
    condition(titleBox) {
      this.titleBox = titleBox;
      this.$refs.condition.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },
    proposals(titleBox) {
      this.titleProposals = titleBox;
      this.$refs.proposals.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },
    explanation(titleBox) {
      this.titleExplanation = titleBox;
      this.$refs.explanation.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },

    handleCancel() {
      this.isVisible = false;
    },
    isConditionCancel() {
      this.isCondition = false;
    },
    formatter(value) {
      if (value == "0") {
        return "";
      } else if (value < 10) {
        return "0" + value + ":00";
      } else {
        return value + ":00";
      }
    },
    lczzjl() {
      this.$refs.lczzjl.visible = true;
    },
    onAcitveDmChange() {
      // else if (this.ruleForm.activityTypeDm === '32') {
      //   // 32：参加会前集中视察 最小单位为日，去掉时间及上下午内容
      //   this.chooseActiveType = 2
      // } 
      // 18：其他活动：去掉时间选项
      if (this.ruleForm.activityTypeDm === '18') {
        this.chooseActiveType = 1
      } else {
        // 去掉上下午
        this.chooseActiveType = 0
      }
    }
  },
  watch: {
    'ruleForm.activityTypeDm': 'onAcitveDmChange'
  }
};
</script>

<style lang="scss" scoped>
.confirm {
  background-color: #d92b3e;
  color: #fff;
}

.confirm:hover {
  border-color: #d92b3e !important;
}

.register-box {
  .Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.formData {
  margin-right: 4%;
  margin-top: 1.5%;
}

.textarea {
  width: 400% !important;
  margin-right: -475px !important;
}

.table {
  width: 780px;
  margin-left: -158px;
}
</style>
