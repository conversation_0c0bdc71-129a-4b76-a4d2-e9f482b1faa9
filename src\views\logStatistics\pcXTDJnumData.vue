<template>
  <div class="knowledge table-container">

    <a-spin :indicator="indicator" :spinning="listLoading">
      <div style="width:100% ;margin-left:0">
        <a-form-model layout="horizontal" :model="queryForm">
          <div>
            <a-row style="margin-left:1%"	>
              <a-col :md="6" :sm="24">
                <a-form-model-item label="年份" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.time" placeholder="请选择年份" allow-clear show-search>
                    <a-select-option v-for="item in yearList" :key="item" :label="item" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-model-item label="姓名" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-input allow-clear v-on:keyup.enter="search" v-model="queryForm.userName" placeholder="请输入姓名" />
                </a-form-model-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-model-item label="是否是代表" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model.number="queryForm.isBehalf" placeholder="请选择是否是代表" allow-clear show-search>
                    <a-select-option label="是" :value="1">是</a-select-option>
                    <a-select-option label="否" :value="0">否</a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <span style="float: right; margin-top: 3px;">
                  <a @click="toggleAdvanced" style="margin-right: 8px;">
                    {{ advanced ? "收起" : "高级搜索" }}
                    <a-icon :type="advanced ? 'up' : 'down'" />
                  </a>
                  <a-button style="margin-top: 4px;" type="primary" @click="search">搜索</a-button>
                  <a-button style="margin-left: 12px;" @click="reset" class="pinkBoutton">重置</a-button>
                </span>
              </a-col>

            </a-row>
            <a-row style="margin-left:1%"	 v-if="advanced">
              <a-col :md="6" :sm="24">
                <a-form-model-item label="子系统" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.menuId" placeholder="请选择子系统" allow-clear show-search>
                    <a-select-option v-for="item in zxzheiTitle" :key="item.SUB_SYSTEM_ID" :label="item.MENU"
                      :value="item.SUB_SYSTEM_ID">
                      {{ item.MENU }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-model-item label="部门名称" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-input allow-clear v-on:keyup.enter="search" v-model="queryForm.deptName" placeholder="请输入部门名称" />
                </a-form-model-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="开始时间" prop="startTime" :labelCol="{ span: 5 }"
                  :wrapperCol="{ span: 18, offset: 0 }">
                  <a-date-picker v-model="queryForm.startTime" allow-clear value-format="YYYY-MM-DD "
                    style="width: 100%;" placeholder="选择开始时间" :disabled-date="
                      (current) =>
                        current && queryForm.endTime
                          ? current.valueOf() >=
                          moment(new Date(queryForm.endTime)).valueOf()
                          : false
                    "></a-date-picker>
                </a-form-item>
              </a-col>
              <a-col :md="6" :sm="24">
                <a-form-item label="结束时间" prop="endTime" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-date-picker v-model="queryForm.endTime" allow-clear value-format="YYYY-MM-DD" style="width: 100%;"
                    placeholder="选择结束时间" :disabled-date="
                      (current) =>
                        current && queryForm.startTime
                          ? moment(new Date(queryForm.startTime)).valueOf() >=
                          current.valueOf()
                          : false
                    "></a-date-picker>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </a-form-model>
        <a-table :columns="columns" :rowKey="
          (record, index) => {
            return record.USER_ID + index;
          }
        " :dataSource="dataSource" :pagination="pagination">
          <!-- <div slot="Filetitle" slot-scope="{ record }">
          <div v-html="record.title"  ></div>
        </div>  :customRow="clickRow"-->
        </a-table>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { instance_1, instance_4 } from "@/api/axiosRq";
import { myPagination } from "@/mixins/pagination.js";
import Vue from "vue";
import store from "@/store";
import { log } from '@antv/g2plot/lib/utils';

export default {
  mixins: [myPagination],
  data() {
    return {
      zxzheiTitle: [
        { MENU: "代表建议管理", SUB_SYSTEM_ID: 'dbjygl' },
        { MENU: "代表信息管理", SUB_SYSTEM_ID: 'dbxxgl' },
        { MENU: "代表选举管理", SUB_SYSTEM_ID: 'dbxjgl' },
        { MENU: "联系人大常委会", SUB_SYSTEM_ID: 'lxrdcwh' },
        { MENU: "履职平台门户", SUB_SYSTEM_ID: 'lzptmh' },
        { MENU: "联系人民群众", SUB_SYSTEM_ID: 'lxrmqz' },
        { MENU: "履职登记管理", SUB_SYSTEM_ID: 'lzdjgl' },
        { MENU: "履职活动组织", SUB_SYSTEM_ID: 'lzhdzz' },
        { MENU: "在线学习培训", SUB_SYSTEM_ID: 'zxxxpx' },
      ],
      advanced: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      yearList: [],
      treeData: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "children",
      },
      dataSource: [
      ],

      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "子系统名称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "MENU",
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "用户名称",
          dataIndex: "USER_NAME",
          align: "center",
          width: 250,
          ellipsis: true,
          //  scopedSlots: { customRender: "Filetitle" },
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "部门名称",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ORG_NAME",
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "是否是代表",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ISBEHALF",
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "访问时间",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "ACCESS_TIME",
          customRender: (text, record, index) => {
            return text.replace("T", " ").split("Z").join("").substr(0, 19)
          },
        },
      ],
      queryForm: {
        pageNum: "1",
        pageSize: "10",
        time: '',
        isBehalf: null,
        menuId: null,
      },
      // // 分页器设置
      // pagination: {
      //   pageNo: 1,
      //   pageSize: 10, // 默认每页显示数量
      //   showSizeChanger: true, // 显示可改变每页数量
      //   showQuickJumper: true, // 显示跳转功能
      //   pageSizeOptions: ["10", "20", "50", "100", "200", "500"],
      //   // 每页数量选项
      //   showTotal: (total) => `总共 ${total} 条`, // 显示总数
      //   onShowSizeChange: (current, pageSize) =>
      //     this.changePageSize(current, pageSize), // 改变每页数量时更新显示
      //   onChange: this.handleCurrentChange.bind(this), // 点击页码事件
      //   total: 0, // 总条数
      //   current: 0, // 当前页数
      //   buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
      //   size: "middle",
      // },
      toApi: 'statisticsLog/statisticsVisitLogList'
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "日志统计详情");//PC端各子系统的点击数
    var a = new Date();
    this.yearList[0] = a.getFullYear();
    for (var i = 0; i < 4; i++) {
      var num = this.yearList[this.yearList.length - 1] - 1
      this.yearList.push(num);
    }
    this.queryForm.isBehalf = this.$route.query.isBehalf;
    let { time, } = this.$route.query.queryForm
    this.queryForm.time = time;
    this.queryForm.menuId = this.$route.query.menuId
    this.fetchData()
  },
  methods: {
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    // 重置
    reset() {
      this.queryForm = {
        menuId: this.$route.query.menuId,
        isBehalf: this.$route.query.isBehalf,
        pageNum: "1",
        pageSize: "10",
        time: this.$route.query.queryForm.time,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    fetchData() {
      this.listLoading = true
      instance_1({
        url: this.toApi,
        method: "get",
        params: this.queryForm,
        headers: {
          flag: false
        }
      }).then((res) => {

        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
        this.listLoading = false
      });
    },
    search() {
      this.queryForm.pageNum = 1
      this.queryForm.current = 1
      this.pagination.current = 1
      this.fetchData()
    },

    download(recode) {
      let { doc, id, title } = recode
      console.log(doc);
      // 将斜杆相反
      let doc_file = doc.replace(/\\/g, "/");
      console.log(doc_file);
      if (doc == "") { }
      else if (doc.indexOf('pdf') > -1) {
        // 原先 这里的tonken
        // let pdfUrl =
        //   Vue.prototype.GLOBAL.basePath_1 +"/file/view?file=" +doc +"&token=" +store.getters.accessToken;
        // console.log(pdfUrl);
        //  window.open(
        //   process.env.BASE_URL +
        //   "pdfjs/web/viewer.html?file=" +
        //   encodeURIComponent(pdfUrl) 
        // );
        // +
        let pdfUrl = Vue.prototype.GLOBAL.basePath_1 +
          "/resource/viewPDF?file=" + doc_file + "&token=" + Vue.prototype.GLOBAL.token;
        console.log(pdfUrl);
        window.open(
          process.env.BASE_URL +
          "pdfjs/web/viewer.html?file=" +
          encodeURIComponent(pdfUrl)
        );

      } else {
        instance_1({
          url: "/repository/download",
          method: "get",
          responseType: "blob",
          params: { path: doc, articleId: id }
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          let type = ".doc"
          if (doc.indexOf('xls') > -1) {
            type = ".xls"
          } else {
            type = ".doc"
          }
          a.download = title + type;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        });
      }
    },
    // // 点击行
    // clickRow(record) {
    //   return {
    //     props: {},
    //     on: {
    //       // 事件
    //       click: (event) => {
    //         if ("__vue__" in event.target) {
    //           let content = event.target.attributes[0].textContent;
    //           instance_1({
    //             url: "/home/<USER>/findArticleContent",
    //             method: "get",
    //             params: { contentId: event.target.__vue__.record.id }
    //           }).then((res) => {
    //             this.$router.push({
    //               path: "/training/knowledgeData",
    //               query: {
    //                 data: res.data.data
    //               }
    //             })
    //           });
    //           // if (content.indexOf('-') > -1 && content.indexOf(':') > -1) { }
    //           // else {

    //           // }
    //         }
    //       },
    //     },
    //   };
    // },
  },
};
</script>
<style lang="scss" scoped>
.knowledge {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;

  .title {
    height: 40px;
    line-height: 40px;
    background-color: #fbeeef;
    border-radius: 6px;
    text-align: center;
    font-family: PingFang-M;
  }

  .knowledge-left {
    width: 18%;
    overflow-y: auto;
    height: 600px;
  }

  .knowledge-right {
    margin-left: 2%;
    width: 80%;
  }

  .condition {
    margin-left: 10px;
  }
}
</style>
