<template>
  <div class="knowledge table-container">
        <a-spin :indicator="indicator" :spinning="listLoading">
    <div style="width:100%">
      <a-form-model layout="horizontal" :model="queryForm">
        <div>
          <a-row style="margin-left:1%">
            <a-col :md="6" :sm="24">
              <a-form-model-item label="姓名" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-input allow-clear 
                  v-on:keyup.enter="search"
                v-model="queryForm.logUserName" placeholder="请输入姓名" />
              </a-form-model-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-model-item label="年份" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-select v-model="queryForm.year" placeholder="请选择年份" allow-clear show-search
                 >
                  <a-select-option v-for="item in yearList" :key="item" :label="item" :value="item">
                    {{ item }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-model-item label="系统名称" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-select v-model="queryForm.logBusinessCode" placeholder="请选择系统" allow-clear show-search
                   @change="changelogBusinessCode">
                  <a-select-option v-for="item in dataTotal" :key="item.ids" :label="item.name" :value="item.ids"
                   >
                    {{ item.name }}
                  </a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <span style="float: right; margin-top: 3px;">
                <a @click="toggleAdvanced" style="margin-right: 8px;">
                  {{ advanced ? "收起" : "高级搜索" }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-button type="primary" @click="search()">搜索</a-button>
                <a-button style="margin-left: 12px;" @click="reset" class="pinkBoutton" >重置</a-button>
              </span>
            </a-col>  
          </a-row>
          <a-row style="margin-left:1%" v-if="advanced">
              <a-col :md="6" :sm="24">
              <a-form-model-item label="部门" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-input allow-clear 
                   v-on:keyup.enter="search"
                v-model="queryForm.orgName" placeholder="请输入部门" />
              </a-form-model-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-model-item label="是否是代表" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-select v-model="queryForm.isDb" placeholder="请选择是否是代表" allow-clear show-search
                 >
                  <a-select-option label="是" value="1">是</a-select-option>
                  <a-select-option label="否" value="0">否</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="开始时间" prop="startTime" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-date-picker v-model="queryForm.startTime" allow-clear value-format="YYYY-MM-DD " placeholder="选择开始时间"  style="width:100%;"
                  :disabled-date="
                    (current) =>
                      current && queryForm.endTime
                        ? current.valueOf() >=
                        moment(new Date(queryForm.endTime)).valueOf()
                        : false
                  "></a-date-picker>
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item label="结束时间" prop="endTime" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                <a-date-picker v-model="queryForm.endTime" allow-clear value-format="YYYY-MM-DD" placeholder="选择结束时间" style="width:100%;"
                  :disabled-date="
                    (current) =>
                      current && queryForm.startTime
                        ? moment(new Date(queryForm.startTime)).valueOf() >=
                        current.valueOf()
                        : false
                  "></a-date-picker>
              </a-form-item>
            </a-col>
          </a-row>

        </div>
      </a-form-model>
      <a-table :columns="columns" :rowKey="
        (record, index) => {
          return record.logUserName + index;
        }
      "  :dataSource="dataSource" :pagination="pagination">
        <!-- <div slot="Filetitle" slot-scope="{ record }">
          <div v-html="record.title"  ></div>
        </div>  :customRow="clickRow"-->
      </a-table>
    </div>
    </a-spin>
  </div>
</template>

<script>
import { instance_1, instance_4 } from "@/api/axiosRq";
import { myPagination } from "@/mixins/pagination.js";
import Vue from "vue";
import store from "@/store";
import { log } from '@antv/g2plot/lib/utils';

export default {
  mixins: [myPagination],
  data() {
    return {
         // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      advanced: false,
      treeData: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "children",
      },
      dataSource: [
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "用户名称",
          dataIndex: "logUserName",
          align: "center",
          width: 350,
          ellipsis: true,
          //  scopedSlots: { customRender: "Filetitle" },
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "部门",
          align: "center",
          width: 200,
          ellipsis: true,
          dataIndex: "logOrgName",
          customRender: (text, record, index) => {
            // return text.replace("T", " ").split("Z").join("").substr(0, 19)
            return text || '/'
          },
        },
        {
          title: "是否是代表",
          dataIndex: "isDb",
          align: "center",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            var ss = text == '1' ? '是' : '否'
            return ss || '/'
          }
        },
        {
          title: "点击数（单位：次）",
          dataIndex: "num",
          align: "center",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return text || '0'
          }
        },
        // {
        //   fixed: 'right',
        //   title: "操作",
        //   align: "center",
        //   width: 120,
        //   customRender: (text, record, index) => {
        //     let h = this.$createElement;
        //     return h("div", [
        //       h(
        //         "span",
        //         {
        //           attrs: {
        //             type: "text",
        //           },
        //           style: {
        //             cursor: "pointer",
        //             color: "#DB3046",
        //           },
        //           on: {
        //             click: () => {
        //               this.download(record);
        //             },
        //           },
        //         },
        //         record.doc.indexOf('pdf') > -1 ? "预览" : record.doc == "" ? "暂无附件" : "下载附件"
        //       ),
        //     ]);
        //   },
        // },
      ],
      dataTotal: [
        {
          name: '平台门户',
          num: 0,
          type: "top: 40%;left: -5%;",
          ids: 'lzptmh',
        },
        {
          name: '联系人民群众',
          num: 0,
          type: 'top: 20%;left: 5%;',
          ids: 'lxrmqz',
        },
        {
          name: '联系人大常委会',
          num: 0,
          type: 'top: 10%;    left: 35%;',
          ids: 'lxrdcwh',
        },
        {
          name: '履职活动组织',
          num: 0,
          type: '    top: 20%;    right: 4%;',
          ids: 'lzhdzz',
        },
        {
          name: '代表建议管理',
          num: 0,
          type: '    top: 40%;right: -5%;',
          ids: '登录议案建议',
        },
        {
          name: '履职登记管理',
          num: 0,
          type: '    bottom: 26%;    right: -2%;',
          ids: 'lzdjgl',
        },
        {
          name: '代表选举管理',
          num: 0,
          type: '       bottom: 12%;    left: 53%;',
          ids: 'dbxjgl',
        },
        {
          name: '代表信息管理 ',
          num: 0,
          type: '    bottom: 12%;    left: 16%;',
          ids: 'dbxxgl',
        }, {
          name: '在线学习培训',
          num: 0,
          type: '    bottom: 30%;    left: -1%;',
          ids: 'zxxxpx',
        },
      ],
      queryForm: {
        pageNum: 1,
        pageSize: 10,
        year: null,
        logBusinessCode:null
      },
      
      yearList: []
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "日志统计详情");
    // this.dataTree();
      var a = new Date();
       this.yearList[0] = a.getFullYear();
    for (var i = 0; i < 4; i++) {
      var num = this.yearList[this.yearList.length - 1] - 1
      this.yearList.push(num);
    }
    let { time } = this.$route.query.queryForm
    this.queryForm.year = time
    // console.log(this.$route.query.logBusinessCode);
    // 判断属于那个模块
    if (this.$route.query.logBusinessCode) {
      // this.queryForm.logBusinessCode = this.$route.query.logBusinessCode
      // this.$route.query.logBusinessCode != "登录议案建议" ? '' : this.queryForm.logDescription = this.$route.query.logBusinessCode
      // this.$route.query.logBusinessCode != "登录议案建议" ? this.fetchData() : this.fetchDataYAJY();
       this.reset()
    }
   

  },
  methods: { 
    changelogBusinessCode(e) {
      this.queryForm.logBusinessCode = e
    },
    reset() {
      this.queryForm = {
        pageNum: 1,
        pageSize: 10,
        year: this.$route.query.queryForm.time,
        logBusinessCode: this.$route.query.logBusinessCode
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      // this.queryForm.logBusinessCode != "登录议案建议" ? '' : this.queryForm.logDescription = this.$route.query.logBusinessCode;
      this.queryForm.logBusinessCode != "登录议案建议" ? this.fetchData() : this.fetchDataYAJY()

    }, 
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    fetchData() {
      this.listLoading=true;
      var Path=''
      if(this.queryForm.logBusinessCode != "登录议案建议"){
          Path="/SysLogController/systemsScondaryList"
      }else{
        Path="/SysLogController/loginScondaryList"
      }
      // 因为分页会用到改接口
      instance_1({
        url: Path,
        method: "get",
        params: this.queryForm
      }).then((res) => {
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
         this.listLoading=false;
      });
    },
    // 议案建议
    fetchDataYAJY() {
       this.listLoading=true;
      instance_1({
        url: "/SysLogController/loginScondaryList",
        method: "get",
        params: this.queryForm
      }).then((res) => {
        this.dataSource = res.data.rows;
        this.pagination.total = res.data.total;
         this.listLoading=false;
      });
    },
    search() {
      this.queryForm.pageNum = 1
      this.pagination.current = 1
        // this.queryForm.logBusinessCode != "登录议案建议" ? '' : this.queryForm.logDescription = this.$route.query.logBusinessCode;
      this.queryForm.logBusinessCode != "登录议案建议" ? this.fetchData() : this.fetchDataYAJY()

    },

    download(recode) {
      let { doc, id, title } = recode
      console.log(doc);
      // 将斜杆相反
      let doc_file = doc.replace(/\\/g, "/");
      console.log(doc_file);
      if (doc == "") { }
      else if (doc.indexOf('pdf') > -1) {
        // 原先 这里的tonken
        // let pdfUrl =
        //   Vue.prototype.GLOBAL.basePath_1 +"/file/view?file=" +doc +"&token=" +store.getters.accessToken;
        // console.log(pdfUrl);
        //  window.open(
        //   process.env.BASE_URL +
        //   "pdfjs/web/viewer.html?file=" +
        //   encodeURIComponent(pdfUrl) 
        // );
        // +
        let pdfUrl = Vue.prototype.GLOBAL.basePath_1 +
          "/resource/viewPDF?file=" + doc_file + "&token=" + Vue.prototype.GLOBAL.token;
        console.log(pdfUrl);
        window.open(
          process.env.BASE_URL +
          "pdfjs/web/viewer.html?file=" +
          encodeURIComponent(pdfUrl)
        );

      } else {
        instance_1({
          url: "/repository/download",
          method: "get",
          responseType: "blob",
          params: { path: doc, articleId: id }
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          let type = ".doc"
          if (doc.indexOf('xls') > -1) {
            type = ".xls"
          } else {
            type = ".doc"
          }
          a.download = title + type;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        });
      }
    },
    // 点击行
    clickRow(record) {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              let content = event.target.attributes[0].textContent;
              instance_1({
                url: "/home/<USER>/findArticleContent",
                method: "get",
                params: { contentId: event.target.__vue__.record.id }
              }).then((res) => {
                this.$router.push({
                  path: "/training/knowledgeData",
                  query: {
                    data: res.data.data
                  }
                })
              });
              // if (content.indexOf('-') > -1 && content.indexOf(':') > -1) { }
              // else {

              // }
            }
          },
        },
      };
    },
  },
};
</script>
<style lang="scss" scoped>
.knowledge {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;

  .title {
    height: 40px;
    line-height: 40px;
    background-color: #fbeeef;
    border-radius: 6px;
    text-align: center;
    font-family: PingFang-M;
  }

  .knowledge-left {
    width: 18%;
    overflow-y: auto;
    height: 600px;
  }

  .knowledge-right {
    margin-left: 2%;
    width: 80%;
  }

  .condition {
    margin-left: 10px;
  }
}
</style>
