<template>
  <div class="table-container">
    <a-row>
      <a-form-model layout="horizontal"
                    :model="queryFormPC"
                    :label-col="{ span: 5 }"
                    :wrapper-col="{ span: 18, offset: 1 }"></a-form-model>
      <a-col :span="4">
        <a-form-model-item label="条件"
                           :label-col="{ span: 5 }"
                           :wrapper-col="{ span: 18, offset: 1 }">
          <a-select v-model="queryFormPC.dateType"
                    placeholder="请选择条件"
                    show-search
                    style="width:100%"
                    @change="changeType">
            <a-select-option v-for="item in queryTypeOptions"
                             :key="item.value"
                             :label="item.label"
                             :value="item.value">
              {{  item.label  }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col v-if="queryFormPC.dateType == 'year'"
             :span="5">
        <a-form-model-item label="年份"
                           :label-col="{ span: 5 }"
                           :wrapper-col="{ span: 18, offset: 1 }">
          <a-select v-model="queryFormPC.time"
                    placeholder="请选择年份"
                    show-search
                    style="width:100%"
                    @change="changeYear">
            <a-select-option v-for="item in yearList"
                             :key="item"
                             :label="item"
                             :value="item">
              {{  item  }}
            </a-select-option>
          </a-select>
        </a-form-model-item>
      </a-col>
      <a-col v-if="queryFormPC.dateType == 'month'"
             :span="5">
        <a-form-model-item label="月份"
                           :label-col="{ span: 5 }"
                           :wrapper-col="{ span: 18, offset: 1 }">
          <a-month-picker v-model="queryFormPC.startTime"
                          style="width: 100%;"
                          value-format="YYYY-MM" />
        </a-form-model-item>
      </a-col>
      <a-col v-if="queryFormPC.dateType == 'day'"
             :md="5"
             :sm="24">
        <a-form-item label="开始时间"
                     prop="startTime"
                     :label-col="{ span: 6 }"
                     :wrapper-col="{ span: 17, offset: 1 }">
          <a-date-picker v-model="queryFormPC.startTime"
                         value-format="YYYY-MM-DD "
                         placeholder="选择开始时间"
                         style="width: 100%"
                         :disabled-date="
              (current) =>
                current && queryFormPC.endTime
                  ? current.valueOf() >=
                  moment(new Date(queryFormPC.endTime)).valueOf()
                  : false
            "></a-date-picker>
        </a-form-item>
      </a-col>
      <a-col v-if="queryFormPC.dateType == 'day'"
             :md="5"
             :sm="24">
        <a-form-item label="结束时间"
                     prop="endTime"
                     :label-col="{ span: 6 }"
                     :wrapper-col="{ span: 17, offset: 1 }">
          <a-date-picker v-model="queryFormPC.endTime"
                         value-format="YYYY-MM-DD"
                         placeholder="选择结束时间"
                         style="width: 100%"
                         :disabled-date="
              (current) =>
                current && queryFormPC.startTime
                  ? moment(new Date(queryFormPC.startTime)).valueOf() >=
                  current.valueOf() ||
                  moment(new Date(queryFormPC.startTime.slice(0, 4) + '-12-31')).valueOf() <= current.valueOf()
                  : false
            "></a-date-picker>
        </a-form-item>
      </a-col>
      <a-col :span="5"
             style="float: right;">
        <span style="float: right; margin-top:5px">
          <a-button type="primary"
                    @click="search">搜索</a-button>
          <a-button style="margin-left: 12px;"
                    class="pinkBoutton"
                    @click="reset">重置</a-button>
          <a-button type="primary"
                    :loading="downloadLoading"
                    style="margin-left: 8px;"
                    @click="download">导出</a-button>
          <!--   :loading="downloadLoading" -->
        </span>
      </a-col>
      <a-form-model></a-form-model>
    </a-row>
    <a-row>

      <!-- 左边 -->
      <a-col :span="7">

        <!-- pc -->
        <div v-if="mode == 'PC'"
             class="PCright">
          <div class="PCright_box"
               style="height:18%">
            <a-spin :indicator="indicator"
                    :spinning="pcLeftListLoading">
              <div class="pcleft_one">
                <div class="tit">
                  <p>PC端系统登录人次</p>
                </div>
                <div class="pcleft_one_box"
                     style="padding-left: 20px;   margin-bottom: 30px;">
                  <a class="pcleft_one_dl"
                     @click="topcDLrcData('1')">
                    <p class="num">{{  loginPeople.BEHALFNUM  }}<span>次</span></p>
                    <p class="category">代表人员</p>
                  </a>
                  <a class="pcleft_one_dl"
                     @click="topcDLrcData('0')">
                    <p class="num">{{  loginPeople.NOTBEHALFNUM  }}<span>次</span></p>
                    <p class="category">非代表人员</p>
                  </a>
                </div>
              </div>
            </a-spin>
          </div>
          <div class="PCright_box"
               style="height:40%">
            <a-spin :indicator="indicator"
                    :spinning="pcLeftListLoading">
              <div class="pcleft_two">
                <div class="tit">
                  <p>PC端系统登录次数</p>
                </div>
                <div id="pcDLnum"
                     style="min-height:200px;margin-bottom:30px;">

                </div>
              </div>
            </a-spin>
          </div>
          <div class="PCright_box"
               style="height:38%">
            <a-spin :indicator="indicator"
                    :spinning="pcLeftsListLoading">
              <div class="pcleft_three">
                <div class="tit">
                  <p>PC端系统累计操作次数</p>
                </div>
                <div style="width:80% ;padding-top: 20px; padding-left:20px">
                  <a-table :columns="PCcolumns"
                           :pagination="false"
                           :data-source="PCtabData"
                           bordered>
                    <!--  :customRow="clickRow" -->
                    <template slot="name"
                              slot-scope="text ,record">
                      <a style="    color: black;    width: 100%;    display: inline-block;"
                         @click="clicktoRow(record)">
                        {{  text  }}</a>
                    </template>
                    <template slot="number"
                              slot-scope="text,record">
                      <a style="    color: black;    width: 100%;    display: inline-block;"
                         @click="clicktoRow(record)">
                        {{  text  }}</a>
                    </template>
                  </a-table>
                </div>
              </div>
            </a-spin>
          </div>
        </div>
        <!-- xcx -->
        <div v-if="mode == 'XCX'"
             class="PCright">
          <div class="PCright_box">

            <div class="tit">
              <p>小程序-代表登录情况</p>
            </div>
            <div id="xcxDJnum"
                 style="min-height:280px;margin-top:-30px;margin-bottom:30px;;margin-top:-30px;margin-bottom:30px;">

            </div>
          </div>
          <div class="PCright_box">
            <div class="tit">
              <p>小程序各栏目情况统计</p>
            </div>
            <div id="xcxGLMtj"
                 style="min-height:280px;margin-top:-30px;margin-bottom:30px;">

            </div>
          </div>
        </div>
      </a-col>

      <a-col :span="10">
        <div class="mindel_box">
          <div class="mindel_tit">
            <div class="mindel_titBOX"
                 style="border-radius: 8px 0px 0px 8px"
                 :class="mode == 'PC' ? 'actvie_titBOX' : ''"
                 @click="changeTit('PC')">
              <p>PC端数据统计</p>
            </div>
            <div class="mindel_titBOX"
                 style="border-radius: 0px 8px 8px 0px "
                 :class="mode == 'XCX' ? 'actvie_titBOX' : ''"
                 @click="changeTit('XCX')">
              <p>小程序数据统计</p>
            </div>
          </div>
        </div>
        <!-- <a-spin :indicator="indicator" :spinning="listLoading"> -->
        <div class="mindel_tow">
          <img src="@/assets/images/indexPage/tjbj.png" />
          <div class="ALLTitle">代表履职服务系统
            <div class="sTitle">
              <p>(访问数汇总)</p>
            </div>
          </div>
          <div v-for="(item, index) in dataTotal"
               :key="index"
               class="min_box"
               :style="item.type"
               @click="toXQ(item.ids)">
            <div class="min_box_tit">{{  item.name  }}
              <span class="min_box_allnum">({{  item.allnum.count  }}次)</span>
            </div>

            <p class="min_box_num">(代表 :{{  item.allnum.DB  }}次/非代表:{{  item.allnum.FDB  }}次)</p>
          </div>
        </div>
        <!-- </a-spin> -->
      </a-col>
      <!-- 右边 -->
      <a-col :span="7">
        <!-- pc -->
        <div v-if="mode == 'PC'"
             class="PCright">
          <div class="PCright_box">
            <a-spin :indicator="indicator"
                    :spinning="pcRightListLoading">
              <div class="tit">
                <p>PC端各子系统的点击数</p>
              </div>
              <div v-if="mode == 'PC'"
                   id="pcDJnum"
                   style="min-height:280px;margin-bottom:30px;">

              </div>
            </a-spin>
          </div>
          <div class="PCright_box">
            <a-spin :indicator="indicator"
                    :spinning="pcRightListLoading">
              <div class="tit">
                <p>PC端各子系统新增数据情况统计</p>
              </div>
              <div class="ttbox">
                <div class="dw">单位：次</div>
                <div v-if="mode == 'PC'"
                     id="pcADDnum"
                     style="min-height:280px;margin-top:-30px;margin-bottom:30px;">

                </div>
              </div>
            </a-spin>
          </div>
        </div>
        <!-- 小程序 -->
        <div v-if="mode == 'XCX'"
             class="PCright">
          <div class="PCright_box">

            <div class="tit">
              <p>小程序-代表点击趋势统计</p>
            </div>

            <div v-if="mode == 'XCX'"
                 id="xcxDJqushi"
                 style="min-height:280px;margin-top:-30px;margin-bottom:30px;">

            </div>
          </div>
          <div class="PCright_box">
            <div class="tit">
              <p>小程序各栏目新增数据情况统计</p>
            </div>
            <div class="ttbox">
              <div class="dw">单位：次</div>
              <div v-if="mode == 'XCX'"
                   id="xcxADDtj"
                   style="min-height:280px;margin-top:-30px;margin-bottom:30px;">
              </div>
            </div>
          </div>
        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import moment from "moment";

// 引入基本模板
let echarts = require("echarts/lib/echarts");

// 引入提示框和title组件
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
// 引入饼状图组件
require("echarts/lib/chart/pie");
// 引入柱状图组件
require("echarts/lib/chart/bar");
// 引入柱状图组件
// 折线
require("echarts/lib/chart/line");

export default {
  name: "Table",
  components: {},
  filters: {},
  data () {
    return {
      TBloading: false,
      yearList: [],
      // pc表格
      PCcolumns: [{
        title: '系统累计操作次数',
        dataIndex: 'name',
        ellipsis: true,
        scopedSlots: { customRender: 'name' },
      },
      {
        title: '数量（次）',
        dataIndex: 'num',
        ellipsis: true,
        scopedSlots: { customRender: 'number' },
      },],
      // pc表格数据
      PCtabData: [
        {
          key: '1',
          name: '代表人员',
          num: '0',
        },
        {
          key: '2',
          name: '非代表人员',
          num: '0',
        },],

      // PC端系统登录人次
      loginPeople: {
        BEHALFNUM: 0,
        NOTBEHALFNUM: 0
      },
      // PC端系统累计操作次数
      // operation:{
      //     BEHALFNUM: 0,
      // NOTBEHALFNUM: 0
      // },
      // 切换
      mode: "PC",
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      pcLeftListLoading: false,
      pcLeftsListLoading: false,
      pcRightListLoading: false,
      advanced: false,  //查看更多
      zhuData: {
        dbBind: null,
        dbNoBind: null,
      },
      // 小程序
      queryForm: {
        queryType: "year",
        userType: "0",
        startTime: moment(new Date()).format("YYYY"),
      },
      queryFormPC: {
        dateType: 'year',//日期类型(天：day,月：month，年：year)
        startTime: null, //开始时间
        endTime: null,//结束时间
        time: moment(new Date()).format("YYYY"),
      },
      queryFormPCzj: {
        startTime: null, //开始时间
        endTime: null,//结束时间
        year: moment(new Date()).format("YYYY"),
      },
      queryTypeOptions: [
        { label: "年份", value: "year" },
        { label: "月份", value: "month" },
        { label: "自定义", value: "day" },
      ],
      dataTotal: [
        {
          name: '平台门户',
          num: 0,
          type: "top: 43%;left: -13%;",
          ids: 'lzptmh',
          subsystemName: 'lzptmh',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '联系人民群众',
          num: 0,
          type: 'top: 24%;left: -1%;',
          ids: 'lxrmqz',
          subsystemName: 'lxrmqz',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '联系人大常委会',
          num: 0,
          type: 'top: 16%;    left: 26%;',
          ids: 'lxrdcwh',
          subsystemName: 'lxrdcwh',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '履职活动组织',
          num: 0,
          type: '    top: 24%;    right: -1%;',
          ids: 'lzhdzz',
          subsystemName: 'lzhdzz',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '代表建议管理',
          num: 0,
          type: '    top: 43%;right: -14%;',
          ids: '登录议案建议',
          subsystemName: 'yajygl',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '履职登记管理',
          num: 0,
          type: '    bottom: 32%;    right: -8%;',
          ids: 'lzdjgl',
          subsystemName: 'lzdjgl',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '代表选举管理',
          num: 0,
          type: '       bottom: 18%;    right:8%;',
          ids: 'dbxjgl',
          subsystemName: 'dbxjgl',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
        {
          name: '代表信息管理 ',
          num: 0,
          type: '    bottom: 18%;    left: 8%;',
          ids: 'dbxxgl',
          subsystemName: 'dbxxgl',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        }, {
          name: '在线学习培训',
          num: 0,
          type: '    bottom: 32%;    left: -8%;',
          ids: 'zxxxpx',
          subsystemName: 'zxxxpx',
          allnum: {
            count: 0,
            DB: 0,
            FDB: 0,
          }
        },
      ],
      zxzheiTitle: [
        { MENU: "代表建议管理", SUB_SYSTEM_ID: 'dbjygl' },
        { MENU: "代表信息管理", SUB_SYSTEM_ID: 'dbxxgl' },
        { MENU: "代表选举管理", SUB_SYSTEM_ID: 'dbxjgl' },
        { MENU: "联系人大常委会", SUB_SYSTEM_ID: 'lxrdcwh' },
        { MENU: "履职平台门户", SUB_SYSTEM_ID: 'lzptmh' },
        { MENU: "联系人民群众", SUB_SYSTEM_ID: 'lxrmqz' },
        { MENU: "履职登记管理", SUB_SYSTEM_ID: 'lzdjgl' },
        { MENU: "履职活动组织", SUB_SYSTEM_ID: 'lzhdzz' },
        { MENU: "在线学习培训", SUB_SYSTEM_ID: 'zxxxpx' },
      ],

      downloadLoading: false,
    };
  },
  computed: {},
  watch: {
    'queryFormPC.startTime': {
      handler: function () {
        if (this.queryFormPC.startTime == null || this.queryFormPC.startTime == '') {
          this.queryFormPC.endTime = ''
        }
      }
    }
  },
  created () {

    var a = new Date();
    this.yearList[0] = a.getFullYear();
    for (var i = 0; i < 4; i++) {
      var num = this.yearList[this.yearList.length - 1] - 1
      this.yearList.push(num);
    }
    if (this.mode == 'PC') {
      this.getPCChartApi()
    } else {
      this.getChartApi();
    }
    this.fetchData()

  },
  mounted () {
  },

  methods: {
    changeType () {
      if (this.queryFormPC.dateType == 'year') {
        this.queryFormPC.time = moment(new Date(new Date())).format('YYYY')
        this.queryFormPC.startTime = ''
        this.queryFormPC.endTime = ''

      } else if (this.queryFormPC.dateType == 'month') {
        this.queryFormPC.time = moment(new Date(new Date())).format('YYYY')
        this.queryFormPC.startTime = moment(new Date(new Date())).format('YYYY-MM')
        this.queryFormPC.endTime = moment(new Date(new Date())).format('YYYY-MM')

      } else {
        this.queryFormPC.time = ''
        this.queryFormPC.startTime = moment(new Date(new Date())).format('YYYY-MM-DD')
        this.queryFormPC.endTime = '';

      }
    },
    search () {
      if (this.queryFormPC.dateType == 'month') {
        this.queryFormPC.time = this.queryFormPC.startTime.slice(0, 4)
        this.queryFormPC.endTime = this.queryFormPC.startTime

      } else if (this.queryFormPC.dateType == 'day') {
        if (this.queryFormPC.startTime == '' || this.queryFormPC.endTime == '') {
          this.$message.warning("请填写完整搜索时间");
          return
        } else {
          this.queryFormPC.time = this.queryFormPC.startTime.slice(0, 4)
        }
      }
      this.fetchData();
      this.getPCChartApi()
    },
    reset () {
      this.queryFormPC = {
        dateType: 'year',//日期类型(天：day,月：month，年：year)
        startTime: null, //开始时间
        endTime: null,//结束时间
        time: moment(new Date()).format("YYYY"),
      };
      this.fetchData();
      this.getPCChartApi()
    },
    changeYear (e) {

    },
    // 导出
    download () {
      this.downloadLoading = true;
      if (this.queryFormPC.dateType == 'month') {
        this.queryFormPC.time = this.queryFormPC.startTime.slice(0, 4)
        this.queryFormPC.endTime = this.queryFormPC.startTime

      } else if (this.queryFormPC.dateType == 'day') {
        if (this.queryFormPC.startTime == '' || this.queryFormPC.endTime == '') {
          this.$message.warning("请填写完整搜索时间");
          return
        } else {
          this.queryFormPC.time = this.queryFormPC.startTime.slice(0, 4)
        }
      }
      let name = ''
      if (this.queryFormPC.dateType == 'year') {
        name = this.queryFormPC.time + '年';
      } else if (this.queryFormPC.dateType == 'month') {
        name = this.queryFormPC.startTime.slice(0, 4) + '年' + this.queryFormPC.startTime.slice(5, 7) + '月';
      } else {
        let state = this.queryFormPC.startTime.slice(0, 4) + '年' +
          this.queryFormPC.startTime.slice(5, 7) + '月' +
          this.queryFormPC.startTime.slice(8, 10) + '日';
        let end = this.queryFormPC.endTime ? (this.queryFormPC.endTime.slice(0, 4) + '年' +
          this.queryFormPC.endTime.slice(5, 7) + '月' +
          this.queryFormPC.endTime.slice(8, 10) + '日') : null;
        name = end ? state + '至' + end : state;
      }
      instance_1({
        url: "/SysLogController/exportSyslogToWord",
        method: "get",
        responseType: "blob",
        params: this.queryFormPC,
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;

        a.download = `代表履职服务系统` + name + `PC统计数据.doc`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
        setTimeout(() => {
          this.downloadLoading = false;
        }, 1000)
      });

    },
    toXQ (type) {
      this.$router.push({ path: "/systemData/Statistics", query: { queryForm: this.queryFormPC, logBusinessCode: type } });
    },
    // PC端系统累计操作次数 operation
    clickRow () {
      return {
        props: {},
        on: {
          // 事件
          click: (event) => {
            if ("__vue__" in event.target) {
              // console.log(event.target, event)
              this.$router.push({
                path: "/systemData/pcDLnumData",
                query: { queryForm: this.queryFormPC, type: 'PC端系统累计操作次数' },
              });
            }
          },
        },
      };
    },
    clicktoRow (record) {
      this.$router.push({
        path: "/systemData/pcDLnumData",
        query: { queryForm: this.queryFormPC, type: 'PC端系统累计操作次数', isBehalf: record.name == "代表人员" ? 1 : 0 },
      });
    },
    // 切换主体
    changeTit (e) {
      this.mode = e;
      if (e == 'PC') {
        this.fetchData();
        this.getPCChartApi();
        // 跳转  /systemData/newlogStatistics
        // this.$router.push({ path: "/systemData/newlogStatistics" });
      } else {
        // this.getChartApi();

        // 跳转   /systemData/XCXlogStatistics
        this.$router.push({ path: "/systemData/XCXlogStatistics" });
      }
    },
    toggleAdvanced () {
      this.advanced = !this.advanced;
    },
    // pc数据
    getPCChartApi () {
      this.pcLeftListLoading = true;
      this.pcRightListLoading = true;
      this.pcLeftsListLoading = true;
      instance_1({
        method: "get",
        url: "/statisticsLog/statisticsLogCountLeft",  //
        params: this.queryFormPC,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.loginPeople = res.data.data.loginPeople; //PC端系统登录人次
          var loginAllPeople = res.data.data.loginAllPeople;
          this.getPCdl(loginAllPeople);//PC端系统登录次数 
          // this.PCtabData[0].num = res.data.data.operation.BEHALFNUM;
          // this.PCtabData[1].num = res.data.data.operation.NOTBEHALFNUM;
          // this.$forceUpdate();
          this.pcLeftListLoading = false;
        }
      });
      instance_1({
        method: "get",
        url: "/statisticsLog/statisticsOperationCount",  //PC端系统累计操作次数
        params: this.queryFormPC,
      }).then((res) => {
        if (res.data.code == "0000") {
          // this.operation=res.data.data.operation; //PC端系统累计操作次数
          this.PCtabData[0].num = res.data.data.operation.BEHALFNUM;
          this.PCtabData[1].num = res.data.data.operation.NOTBEHALFNUM;
          this.$forceUpdate();
          this.pcLeftsListLoading = false;
        }

      });
      instance_1({
        method: "get",
        url: "/statisticsLog/statisticsLogCountRight",  //
        params: this.queryFormPC,
      }).then((res) => {
        if (res.data.code == "0000") {
          var addTJ = res.data.data.allSystem;
          addTJ.shift()
          this.getPCaddTJ(addTJ)//PC端各子系统新增数据情况统计
          // 生成俩个数组
          let arr1 = res.data.data.visitBehalf;
          let arr2 = res.data.data.visitNotBehalf;
          var value1 = [];
          var value2 = [];
          this.zxzheiTitle.forEach((item, index) => {
            const db = arr1.filter((i) => {
              if (i.SUB_SYSTEM_ID == item.SUB_SYSTEM_ID) {
                i.MENU = item.MENU
              }
              return i.SUB_SYSTEM_ID == item.SUB_SYSTEM_ID
            })
            if (db.length !== 0) {
              value1.push(db[0].NUM)
            } else {
              value1.push(0)
            }

            const Nodb = arr2.filter((i) => {
              return i.SUB_SYSTEM_ID == item.SUB_SYSTEM_ID
            })
            if (Nodb.length !== 0) {
              value2.push(Nodb[0].NUM)
            } else {
              value2.push(0)
            }
          })
          this.getPCdj(value1, value2);
          this.pcRightListLoading = false;
        }
      });

    },
    fetchData () {
      this.listLoading = true;
      // 中间数据
      instance_1({
        method: "get",
        url: "/SysLogController/countList",  //
        params: this.queryFormPC,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.dataTotal.forEach((item, index) => {
            var arr = res.data.data.filter((i) => {
              return i.subsystemName == item.subsystemName
            })
            var ys_allnum = { count: 0, DB: 0, FDB: 0, }
            arr.length != 0 ? this.dataTotal[index].allnum = arr[0] : this.dataTotal[index].allnum = ys_allnum
          })

          this.listLoading = false;
        }
      });
    },
    getChartApi () {
      // // 各栏目使用情况
      // instance_1({
      //   method: "get",
      //   url: "/common/log/getDataTotal",
      //   params: this.queryForm,
      // }).then((res) => {
      //   if (res.data.code == "0000") {
      //     this.getLan(res.data.data);
      //   }
      // });

      //  小程序-代表点击趋势统计 折线图
      instance_1({
        method: "get",
        url: "/common/log/getShiyongTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getXCXclickTrendStatistics(res.data.data);
        }
      });
      //  小程序-代表登录情况
      instance_1({
        method: "get",
        url: "/common/log/getDengluTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getXCXlogin(res.data.data);
        }
      });

      // 小程序各栏目情况统计     

      instance_1({
        method: "get",
        url: "/common/log/getMoudleTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getXCXglmStatistics(res.data.data);
        }
      });

      //小程序各栏目新增数据情况统计    
      instance_1({
        method: "get",
        url: "/common/log/getDataTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getXCXglmAdd(res.data.data);
        }
      });
      this.listLoading = false;
    },
    // PC数组数据

    topcDLrcData (e) {
      var isBehalf = '';
      e == '1' ? isBehalf = 1 : isBehalf = 0
      this.$router.push({
        path: "/systemData/pcDLnumData",
        query: { queryForm: this.queryFormPC, type: 'PC端系统登录人次', isBehalf: isBehalf },
      });
    },
    // PC端系统登录次数 环形图
    getPCdl (value) {
      let option = {

        tooltip: {
          trigger: 'item'
        },
        legend: {
          orient: 'vertical',
          right: '5%',  //标签位置
          top: '30%',
        },
        series: [
          {
            name: 'PC端系统登录次数',
            type: 'pie',
            radius: ['50%', '70%'],
            data: [
              { value: value.BEHALFNUM, name: '代表人员' },
              { value: value.NOTBEHALFNUM, name: '非代表人员' },
            ],
            center: ['40%', '50%'], //图像位置
            labelLine: { //图形外文字线
              normal: {
                length: 10,
                length2: 10
              }
            },
            label: {
              formatter: '{b}\n {c}次数',
              edgeDistance: 10,
              lineHeight: 15,
            },
            itemStyle: {
              normal: {
                color: function (params) {
                  // 给出颜色组
                  var colorList = [
                    "rgba(212, 48, 48, 1)",
                    "rgba(255, 141, 26, 1)",
                  ];
                  //调用
                  return colorList[params.dataIndex];
                },
              },
            },
          }
        ]
      };
      var myChart = echarts.init(document.getElementById("pcDLnum"));
      myChart.setOption(option);
      myChart.on("click", (params) => {
        var isBehalf = '';
        params.name == '代表人员' ? isBehalf = 1 : isBehalf = 0
        this.$router.push({
          path: "/systemData/pcDLnumData",
          query: { queryForm: this.queryFormPC, type: 'PC端系统登录次数', isBehalf: isBehalf },
        });
      });
    },
    // pc点击折线图
    getPCdj (value, value2) {
      let zheiTitle = [];
      let Behalf = [];
      let NotBehalf = [];
      value.forEach((item) => {
        Behalf.push(item);
      });
      value2.forEach((item) => {
        NotBehalf.push(item);
      });
      this.zxzheiTitle.forEach((item) => {
        zheiTitle.push(item.MENU);
      })
      let option = {
        title: {
          text: ''
        },
        tooltip: {
          trigger: 'axis'
        },
        legend: {
          data: ['代表人员', '非代表人员',],
          top: '5%',
          right: '0'
        },
        grid: {
          left: '3%',
          bottom: '3%',
          containLabel: true
        },
        // toolbox: {
        //   feature: {
        //     saveAsImage: {}
        //   }
        // },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: zheiTitle,
          axisLabel: {
            rotate: -70,
            interval: 0 //全部显示x轴
          }
        },
        yAxis: {
          type: 'value',
        },
        series: [
          {
            name: '代表人员',
            type: 'line',
            // stack: 'Total',
            icon: 'rect',
            data: Behalf,
            itemStyle: {
              normal: {
                color: "rgba(255, 141, 26, 1)",
                lineStyle: {
                  color: "rgba(255, 141, 26, 1)",
                }
              },
            },
          },
          {
            name: '非代表人员',
            type: 'line',
            // stack: 'Total',
            icon: 'rect',
            data: NotBehalf,
            itemStyle: {
              normal: {
                color: "rgba(212, 48, 48, 1)",
                lineStyle: {
                  color: "rgba(212, 48, 48, 1)",
                }
              },
            },
          },
        ]
      };
      var myChart = echarts.init(document.getElementById("pcDJnum"));
      myChart.setOption(option);
      myChart.on("click", (params) => {
        this.$router.push({
          path: "/systemData/pcXTDJnumData",
          query: {
            queryForm: this.queryFormPC,
            moudleName: params.name,
            isBehalf: params.seriesName == '代表人员' ? 1 : 0,
            menuId: this.zxzheiTitle[params.dataIndex].SUB_SYSTEM_ID
          },
        });
      });
    },
    // PC端各子系统新增数据情况统计
    getPCaddTJ (value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.MENU);
        zheValue.unshift(item.NUM);
      });
      const getColumn = echarts.init(document.getElementById("pcADDnum"));

      let TextBox = "";

      getColumn.setOption({
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        legend: {
          top: '5%',
          right: '0',
          // data: "单位：次数"
        },
        yAxis: {
          type: "category",
          data: zheiTitle,
          // name: "单位：次数",
          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
        },
        xAxis: {
          type: "value",
          splitLine: {
            // 去除背景网格线
            show: false,
          },
          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        grid: {
          left: "12%",
          right: "20%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            data: zheValue,
            type: "bar",
            showBackground: true,
            barWidth: "50%",
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            // label: {
            //   show: true,
            //   position: "right",
            //   formatter: function (params) {
            //     let title = "";
            //     value.forEach((item) => {
            //       if (item.label == params.name) {
            //         title = params.data + "人次";
            //       }
            //     });
            //     return title;
            //   },
            // },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                // data  name
                return params.data + "人次";
              },
            },
            itemStyle: {
              // 柱状图的背景色
              normal: {
                barBorderRadius: [0, 80, 80, 0],
                color: function (params) {
                  // 给出颜色组
                  var colorList = [
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                  ];
                  //调用
                  return colorList[params.dataIndex];
                },
              },
            },
          },
        ],
      });
      getColumn.getZr().on("click", (params) => {
        let ponintInPixel = [params.offsetX, params.offsetY];
        if (getColumn.containPixel('grid', ponintInPixel)) {
          let pointInGrid = getColumn.convertFromPixel({ seriesIndex: 0 }, ponintInPixel)

          let xIndex = pointInGrid[0];
          let yIndex = pointInGrid[1];
          var Indexpath = ' '; //给空直接跳转首页
          var systemCode = '';
          var labelname = zheiTitle[yIndex];
          if (labelname == '平台门户') {
            // Indexpath = '/platformIndex';
            systemCode = "lzptmh"
          } else if (labelname == '联系人民群众') {
            // Indexpath = '/site/index';
            systemCode = "lxrmqz"
          } else if (labelname == '联系人大常委会') {
            // Indexpath = '/InformList';
            systemCode = "lxrdcwh"
          } else if (labelname == '履职活动组织') {
            // Indexpath = '/index';
            systemCode = "lzhdzz"
          } else if (labelname == '代表议案建议管理') {
            // Indexpath = '/recommendedMageIndex'
            systemCode = "dbjygl"
          } else if (labelname == '履职登记管理') {
            // Indexpath = '/registrationMage'
            systemCode = "lzdjgl"
          } else if (labelname == '代表选举管理') {
            // Indexpath = '/RepresentIndex'
            systemCode = "dbxjgl"
          } else if (labelname == '代表信息管理') {
            // Indexpath = '/workIndex'
            systemCode = "dbxxgl"
          } else if (labelname == '在线学习培训') {
            // Indexpath = '/organizationTraining'
            systemCode = "zxxxpx"
          }
          let locationSystem = { path: this.$router.history.current.path, systemCode: this.$router.history.current.meta.systemId };
          sessionStorage.setItem(
            "locationSystem",
            JSON.stringify(locationSystem)
          );

          this.$nextTick(() => {


            window.top.postMessage(
              {
                event: "locationSystem", // 约定的消息事件
                args: {
                  path: Indexpath, //路径
                  systemCode: systemCode, //子系统编码
                } // 参数
              },
              "*"
            );
          });


          // this.$router.push({ path: "/iChartData", query: { queryForm: this.queryForm, moudleName: params.name } });

        }
      });
    },
    // 小程序-代表点击趋势统计
    getXCXclickTrendStatistics (value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        if (this.isColor == "0") {
          zheiTitle.push(item.key + "月");
        } else {
          zheiTitle.push(item.key + "日");
        }
        zheValue.push(item.value);
      });
      let TextBox = "";
      let option = {
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        tooltip: {
          trigger: "axis",
        },
        // legend: {
        //   itemHeight: 15
        // },

        grid: {
          left: "8%",
          right: "6%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          //x轴文字旋转
          // axisLabel: {
          //   rotate: 330,
          //   interval: 0
          // },
          type: "category",
          data: zheiTitle,
        },
        yAxis: {
          splitNumber: 10,
          type: "value",
          // name: "单位：次数"
        },
        series: [
          {
            name: "点击次数",
            data: zheValue,
            type: "line",
          },
        ],
      };
      var myChart = echarts.init(document.getElementById("xcxDJqushi"));
      myChart.setOption(option);
    },
    // 小程序-代表登录情况
    getXCXlogin (value) {
      let zheiTitle = [];
      let zheValue = [];

      value.forEach((item) => {
        if (this.isColor == "0") {
          zheiTitle.push(item.key + "月");
        } else {
          zheiTitle.push(item.key + "日");
        }

        zheValue.push(item.value);
      });
      let TextBox = "";

      let option = {
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        tooltip: {
          trigger: "axis",
        },
        // legend: {
        //   itemHeight: 15
        // },

        grid: {
          left: "8%",
          right: "6%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          //x轴文字旋转
          // axisLabel: {
          //   rotate: 330,
          //   interval: 0
          // },
          type: "category",
          data: zheiTitle,
        },
        yAxis: {
          splitNumber: 10,
          type: "value",
          name: "单位：次数",
        },
        series: [
          {
            name: "登录人次",
            data: zheValue,
            type: "line",
          },
        ],
      };
      var myChart = echarts.init(document.getElementById("xcxDJnum"));
      myChart.setOption(option);
    },
    // 小程序各栏目情况统计
    getXCXglmStatistics (value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.label);
        zheValue.unshift(item.value);
      });
      document.getElementById("xcxGLMtj").setAttribute("_echarts_instance_", "");
      this.behalf = echarts.init(document.getElementById("xcxGLMtj"));
      let TextBox = "";
      this.behalf.setOption(
        {
          title: {
            text: TextBox,
            padding: [2, 0, 0, 14],
          },
          yAxis: {
            type: "category",
            data: zheiTitle,
            name: "单位：次数",
            axisTick: {
              // 刻度
              show: false, // 不显示刻度线
            },
            axisLine: {
              // 设置轴线
              show: false,
            },
          },
          xAxis: {
            type: "value",
            splitLine: {
              // 去除背景网格线
              show: false,
            },
            axisTick: {
              // 刻度
              show: false, // 不显示刻度线
            },
            axisLine: {
              // 设置轴线
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          grid: {
            left: "12%",
            right: "22%",
            bottom: "0%",
            containLabel: true,
          },
          series: [
            {
              data: zheValue,
              type: "bar",
              showBackground: true,
              barWidth: "50%",
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              label: {
                show: true,
                position: "right",
                formatter: function (params) {
                  // data  name
                  return params.data + "人次";
                },
              },
              itemStyle: {
                // 柱状图的背景色
                normal: {
                  barBorderRadius: [0, 80, 80, 0],
                  color: function (params) {
                    // 给出颜色组
                    var colorList = [
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                      "rgba(212, 48, 48, 1)",
                    ];
                    //调用
                    return colorList[params.dataIndex];
                  },
                },
              },
            },
          ],
        },
        true
      );
      this.behalf.on("click", (params) => {
        // console.log(params);
        // this.$router.push({
        //   path: "/iChartData",
        //   query: { queryForm: this.queryForm, moudleName: params.name },
        // });
      });
    },

    // 小程序各栏目新增数据情况统计
    getXCXglmAdd (value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.label);
        zheValue.unshift(item.value);
      });
      const getColumn = echarts.init(document.getElementById("xcxADDtj"));

      let TextBox = "";

      getColumn.setOption({
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        yAxis: {
          type: "category",
          data: zheiTitle,
          name: "单位：次数",
          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
        },
        xAxis: {
          type: "value",
          splitLine: {
            // 去除背景网格线
            show: false,
          },
          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        grid: {
          left: "12%",
          right: "20%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            data: zheValue,
            type: "bar",
            showBackground: true,
            barWidth: "50%",
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                let title = "";
                value.forEach((item) => {
                  if (item.label == params.name) {
                    title = params.data + item.unit;
                  }
                });
                return title;
              },
            },
            itemStyle: {
              // 柱状图的背景色
              normal: {
                barBorderRadius: [0, 80, 80, 0],
                color: function (params) {
                  // 给出颜色组
                  var colorList = [
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                    "rgba(212, 48, 48, 1)",
                  ];
                  //调用
                  return colorList[params.dataIndex];
                },
              },
            },
          },
        ],
      });
      getColumn.on("click", (params) => {
        // this.$router.push({ path: "/iChartData", query: { queryForm: this.queryForm, moudleName: params.name } });
      });
    },
    checkPermission,
  },
};
</script>
<style>
/* IE 浏览器 */
.scrollbar {
  /*三角箭头的颜色*/
  scrollbar-arrow-color: rgb(71, 69, 69);
  /*滚动条滑块按钮的颜色*/
  scrollbar-face-color: #9999;
  /*滚动条整体颜色*/
  scrollbar-highlight-color: #9999;
  /*滚动条阴影*/
  scrollbar-shadow-color: #9999;
  /*滚动条轨道颜色*/
  scrollbar-track-color: #fff;
  /*滚动条3d亮色阴影边框的外观颜色——左边和上边的阴影色*/
  scrollbar-3dlight-color: #9999;
  /*滚动条3d暗色阴影边框的外观颜色——右边和下边的阴影色*/
  scrollbar-darkshadow-color: #9999;
  /*滚动条基准颜色*/
  scrollbar-base-color: #9999;
  border: 0px;
  border-radius: 10px !important;
}

.ehArts-box {
  height: 100%;
  width: 100%;
}

.ehArts {
  margin: 40px 0;
  height: 600px;
  width: 100%;
}

.ehArts-box .beizhu {
  padding: 0 0.4rem;
  margin: 0;
  text-align: left;
  margin-bottom: 1.8rem;
}

.ttbox {
  position: relative;
}

.ttbox .dw {
  position: absolute;
  top: 5%;
  right: 5%;
}
</style>
<style lang="scss" scoped>
.tit {
  border-left: 5px solid #cc3131;
  text-align: left;
  padding-left: 15px;
  margin: 10px 10px;
  margin-bottom: 12px;
  height: 18px;
  display: flex;
  align-items: center;

  p {
    font-weight: bold;
    height: 30px;
    line-height: 30px;
    margin: 0;
    color: rgba(17, 17, 17, 1);
  }
}

.PCleft {
  margin: 8px auto;
  margin-right: 50px;
  height: 700px;
  padding: 10px 20px;
  background: white;
  border: 1px solid #e9e9e9;
  box-shadow: 0px -2px 1px #eeeeef;
  border-radius: 8.01px;
  width: 90%;
}

.pcleft_one .pcleft_one_box {
  display: flex;

  .pcleft_one_dl {
    min-width: 120px;
    margin: 10px;

    .num {
      height: 20px;
      line-height: 20px;
      color: rgba(212, 48, 48, 1);
      font-size: 20px;
      font-weight: 400;

      margin-bottom: 5px !important;

      span {
        font-size: 14px;
      }
    }

    .category {
      color: rgba(51, 51, 51, 1);
      font-size: 14px;
      font-weight: 400;
    }
  }
}

.PCright {
  margin: 10px auto;
  height: 680px;
  padding: 10px 10px;

  width: 90%;
}

.PCright_box {
  height: 330px;
  background: white;
  border: 1px solid #e9e9e9;
  box-shadow: 0px -2px 1px #eeeeef;
  border-radius: 8.01px;
  margin-bottom: 5%;
}

.mindel_box {
  margin: 10px auto;
}

.mindel_tit {
  display: flex;
  justify-content: center;
  align-items: center;
}

.mindel_tit .mindel_titBOX {
  // width: 40%;
  width: 222px;
  height: 52px;
  background: rgba(255, 255, 255, 1);
  border: 1px solid rgba(212, 48, 48, 1);
  border-radius: 8px, 0px, 0px, 8px;

  p {
    width: 100%;
    height: 25px;
    line-height: 52px;
    text-align: center;
    color: rgba(212, 48, 48, 1);
    font-size: 17px;
    font-weight: 500;
  }
}

.mindel_box .mindel_tit .actvie_titBOX {
  background: rgba(212, 48, 48, 1);
  color: rgba(255, 255, 255, 1);

  P {
    font-weight: 500;
    color: rgba(255, 255, 255, 1);
  }
}

.mindel_tow {
  width: 100%;
  min-width: 500px;
  min-height: 500px;
  position: relative;

  img {
    width: 100%;
    position: relative;
  }
}

.ALLTitle {
  font-size: 22px;
  font-weight: 600;
  text-align: center;
  width: 90px;
  color: white;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  .sTitle {
    display: flex;
    justify-content: center;
    align-items: center;

    p {
      font-size: 14px;
      min-width: 250px;
      font-weight: 400;
      /* padding: 10px 0; */
      margin: 0 auto;
      text-align: center;
      display: block;
    }
  }
}

.min_box {
  width: 50%;
  text-align: center;
  position: absolute;
  cursor: pointer;

  .min_box_tit {
    color: rgba(212, 48, 48, 1);
    font-size: 16px;
    font-weight: bold;

    .min_box_allnum {
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 141, 26, 1);
    }
  }

  .min_box_num {
    font-size: 12px;
    text-align: center;
    color: rgba(255, 87, 51, 1);
  }
}

.download {
  position: absolute;
  right: 3%;
  top: -3%;
  z-index: 1;
}
</style>