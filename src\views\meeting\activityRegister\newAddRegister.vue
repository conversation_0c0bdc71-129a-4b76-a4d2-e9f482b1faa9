<template>
  <div class="table-container">
    <!-- <div class="table-title">履职活动</div> -->
    <!-- <ViewTips :tipValue="tipValue" /> -->
    <div class="active-content">
        <a-spin :spinning="iSpinning">
          <div class="title_all" style="margin-bottom: 10px;">
            <h3 class="active-title">履职活动录入</h3>
          </div>
          <a-form-model
                    layout="vertical"
                    ref="ruleForm"
                    :model="ruleForm"
                    :rules="rules"
                    :label-col="{ span: 7 }"
                    :wrapper-col="{ span: 23 }"
                  >
                    <div class="active-form">
                      <!-- <a-row :gutter="15">
                      <a-col :span="24"> -->
                        <a-row type="flex">
                          <!-- <a-col :span="8">
                            <a-form-model-item label="届别" prop="jcDm">
                              <a-select
                                v-model="ruleForm.jcDm"
                                :disabled="showDisabled"
                                placeholder="请选择"
                                allow-clear
                              >
                                <a-select-option
                                  v-for="item in periodList"
                                  :key="item.jcDm"
                                  :value="item.jcDm"
                                  >{{ item.levelName }}</a-select-option
                                >
                              </a-select>
                            </a-form-model-item>
                          </a-col> -->
                          <a-col :span="24">
                            <a-form-model-item
                              :label="textBox + '名称'"
                              prop="activityName"
                              :label-col="{ span: 7 }"
                              :wrapper-col="{ span: 24 }"
                            >
                              <a-input
                                v-model="ruleForm.activityName"
                                :disabled="showDisabled"
                              ></a-input>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-model-item :label="textBox + '地点'" prop="address">
                              <a-input
                                v-model="ruleForm.address"
                                :disabled="showDisabled"
                              ></a-input>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-model-item
                              :label="textBox + '编号'"
                              prop="activityNo"
                            >
                              <a-input
                                v-model="ruleForm.activityNo"
                                disabled
                                placeholder="系统自动生成"
                                ></a-input
                              >
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-model-item
                              :label="textBox + '类型'"
                              prop="activityTypeDm"
                              :wrapper-col="{ span: 24 }"
                            >
                              <a-select
                                v-model="ruleForm.activityTypeDm"
                                placeholder="请选择"
                                allow-clear
                                >
                                <a-select-option
                                  v-for="item in typeData"
                                  :key="item.activityTypeDm"
                                  :value="item.activityTypeDm"
                                  :disabled="item.isUse == 'N'"
                                  :label="item.activityTypeName"
                                  >{{ item.activityTypeName }}</a-select-option
                                >
                              </a-select>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8" :label-col="{ span: 9 }" :wrapper-col="{ span: 23 }">
                            <a-form-model-item :label="textBox + '性质'">
                              <a-select
                                v-model="ruleForm.activityNatureDm"
                                :disabled="showDisabled"
                                placeholder="请选择"
                                allow-clear
                              >
                                <a-select-option
                                  v-for="item in natureData"
                                  :key="item.activityNatureDm"
                                  :value="item.activityNatureDm"
                                  :label="item.activityNatureName"
                                  >{{ item.activityNatureName }}
                                </a-select-option>
                              </a-select>
                            </a-form-model-item>
                          </a-col>

                          <a-col :span="8">
                            <a-form-model-item
                              :label="textBox + '开始时间'"
                              prop="startTime"
                              style="display: flex"
                              :label-col="{ span: 9 }"
                              :wrapper-col="{ span: 23 }"
                            >
                              <div style="display: flex;">
                                <a-date-picker
                                  v-model="ruleForm.startTime"
                                  value-format="YYYY-MM-DD HH:mm:ss"
                                  :disabled="showDisabled"
                                  allow-clear
                                  :show-time="chooseActiveType !== 1 ? { defaultValue: moment('09:00', 'HH:mm'),format: 'HH:mm', minuteStep: 15, hourStep: 1 } : false"
                                  placeholder="选择开始时间"
                                  :style="{ width: chooseActiveType === 1 ? '75%' : '100%' }"
                                ></a-date-picker>
                                <a-select
                                  v-model="ruleForm.startTimeRange"
                                  style="width: 25%"
                                  :disabled="showDisabled"
                                  v-if="chooseActiveType === 1"
                                >
                                  <a-select-option key="1" value="1"
                                    >上午</a-select-option
                                  >
                                  <a-select-option key="2" value="2"
                                    >下午</a-select-option
                                  >
                                  <a-select-option v-if="iShow" key="3" value="3"
                                    >晚上</a-select-option
                                  >
                                </a-select>
                              </div>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8">
                            <a-form-model-item
                              style="display: flex"
                              :disabled="showDisabled"
                              :label="textBox + '结束时间'"
                              prop="endTime"
                              :label-col="{ span: 9 }"
                              :wrapper-col="{ span: 24 }"
                            >
                              <div style="display: flex;">
                                <a-date-picker
                                  v-model="ruleForm.endTime"
                                  :disabled="showDisabled"
                                  allow-clear
                                  :showTime="chooseActiveType !== 1 ? { defaultValue: moment('09:00', 'HH:mm'),format: 'HH:mm', minuteStep: 15, hourStep: 1 } : false"
                                  value-format="YYYY-MM-DD HH:mm:ss"
                                  placeholder="选择结束时间"
                                  :style="{ width: chooseActiveType === 1 ? '75%' : '100%' }"
                                >
                                </a-date-picker>
                                <a-select
                                  v-model="ruleForm.endTimeRange"
                                  style="width: 25%"
                                  :disabled="showDisabled"
                                  v-if="chooseActiveType === 1"
                                >
                                  <a-select-option key="1" value="1"
                                    >上午</a-select-option
                                  >
                                  <a-select-option key="2" value="2"
                                    >下午</a-select-option
                                  >
                                  <a-select-option v-if="iShow" key="3" value="3"
                                    >晚上</a-select-option
                                  >
                                </a-select>
                              </div>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="24">
                            <a-form-model-item
                              prop="orgName"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 24 }"
                            >
                              <template slot="label">
                                <span style="color:red; margin-right: 4px;">*</span>组织单位
                              </template>
                              <div class="searchStyle">
                                <a-input v-model="ruleForm.orgName" disabled></a-input>
                                <a-button
                                  :disabled="showDisabled"
                                  type="primary"
                                  icon="search"
                                  @click="handlePublish"
                                ></a-button>
                              </div>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="8">
                            <span style="line-height: 38px;">
                              <a-button
                                style="width: 66px; border-radius: 6px; margin-bottom: 10px;"
                                type="primary"
                                icon="plus"
                                @click="addRow()"
                                :disabled="showDisabled"></a-button>
                              (不在机构内，请手工填写)
                            </span>
                              <!-- <div v-if="ruleForm.activityWorkerInfos && ruleForm.activityWorkerInfos.length > 0" class="daxin"> -->
                                <div v-for="(item, index) in ruleForm.activityWorkerInfos" :key="index" class="daxin_i">
                                  <a-input
                                    v-model="item.name"
                                    placeholder="请输入组织单位"
                                    autocomplete="off"
                                    style="width: 50%"
                                    :disabled="showDisabled"
                                  >
                                  </a-input>
                                  <a-button
                                    class="btn deleData"
                                    type="primary"
                                    icon="minus"
                                    @click="jianjianJian1(index)"
                                    :disabled="showDisabled"
                                  ></a-button>
                                </div>
                          </a-col>
                          <a-col :span="8"></a-col>
                          <a-col :span="24">
                            <!-- prop="inviteRangeDesc" -->
                            <a-form-model-item
                              label="受邀范围"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 24 }"
                              prop="inviteRangeDesc"
                            >
                              <!-- <div class="searchStyle">
                                <a-input
                                  v-model="ruleForm.inviteRangeDesc"
                                  disabled
                                ></a-input>
                                <a-button
                                  :disabled="showDisabled"
                                  type="primary"
                                  icon="search"
                                  @click="handleRel"
                                >
                                </a-button>
                              </div> -->
                              <a-button
                                :disabled="showDisabled"
                                type="primary"
                                icon="plus"
                                @click="onAttendMembers"
                                style="width: 66px; border-radius: 6px"
                              ></a-button>
                              <MultiLineText :values="ruleForm.attendMembers" />
                            </a-form-model-item>
                          </a-col>

                          <a-col :span="24"></a-col>
                          <a-col :span="24">
                            <a-form-model-item
                              label="工作人员"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 24 }"
                            >
                              <div class="searchStyle">
                                <a-input v-model="ruleForm.workerDesc" disabled></a-input>
                                <a-button
                                  :disabled="showDisabled"
                                  type="primary"
                                  icon="search"
                                  @click="openJoinTable"
                                ></a-button>
                              </div>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="24">
                            <a-form-model-item
                              :label="textBox + '内容'"
                              prop="activityContent"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 24 }"
                            >
                                <a-textarea
                                  v-model="ruleForm.activityContent"
                                  :disabled="showDisabled"
                                  rows="5"
                                  allow-clear
                                >
                                </a-textarea>
                            </a-form-model-item>
                          </a-col>
                        </a-row>
                        <a-row v-if="editShow" style="margin-top: 10px;">
                        <a-row>
                          <a-col :span="8">
                              <a-form-model-item
                                label="出席情况"
                                :label-col="{ span: 9 }"
                                :wrapper-col="{ span: 23 }"
                              >
                                <a-button type="primary" @click="attendance">
                                  应出席{{ figure.allAttendNum }}人,实际出席人员{{
                                    figure.realAttendNum
                                  }}人,点击查看详情
                                </a-button>
                              </a-form-model-item>
                            </a-col>
                          </a-row>
                          <a-row>
                            <a-col :span="8">
                              <a-form-model-item
                                v-if="fyqkShow"
                                label="发言情况"
                                :label-col="{ span: 9 }"
                                :wrapper-col="{ span: 23 }"
                              >
                                <!-- <a-button type="primary" @click="condition('发言情况')" style="width: 120%;" -->
                                <a-button type="primary" @click="condition('发言情况')"
                                  >发言人数{{ figure.speakerNum }}人,点击查看详情</a-button
                                >
                              </a-form-model-item>
                            </a-col>
                          </a-row>
                          <a-row>
                            <a-col :span="8">
                              <a-form-model-item
                                v-if="fyqkShow"
                                label="质询和询问情况"
                                :label-col="{ span: 9 }"
                                :wrapper-col="{ span: 23 }"
                              >
                                <a-button
                                  type="primary"
                                  @click="explanation('质询和询问情况')"
                                >
                                  提出质询案{{ figure.inquiryNum }}件,提出询问案{{
                                    figure.askNum
                                  }}件,点击查看详情
                                </a-button>
                              </a-form-model-item>
                            </a-col>
                          </a-row>
                          <a-col :span="24" style="margin-top: 10px;">
                            <a-form-model-item
                              label="上传书面报告"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 20 }"
                            >
                              <a-upload
                                :action="action"
                                name="file"
                                :multiple="true"
                                :file-list="fileList"
                                :before-upload="drUpload"
                                @change="statement"
                              >
                                <a-button :disabled="showDisabled" type="primary"
                                :loading = "loading1"
                                  >点击上传</a-button
                                >
                              </a-upload>
                              <div class="pdf_show" v-if="fujianSource.length > 0">
                                <div class="pdf_style" v-for="(item, index) in fujianSource" :key="index" @click="handlePdfView1(item)">
                                  <img class="img_icon" v-if="item.name.includes('pdf')" src="@/assets/pdf.png" />
                                  <img class="img_icon" v-if="item.name.includes('ofd')" src="@/assets/ofd.png" />
                                  <div>{{item.name}}</div>
                                  <a-icon class="close_icon" type="close" @click.stop="deteData(item,index)" />
                                </div>
                              </div>
                            </a-form-model-item>
                          </a-col>
                          <a-col :span="24">
                            <a-form-model-item
                              label="上传附件"
                              :label-col="{ span: 4 }"
                              :wrapper-col="{ span: 20 }"
                            >
                              <a-upload
                                :action="action1"
                                name="file"
                                :multiple="true"
                                :file-list="fileList1"
                                :before-upload="drUpload1"
                                @change="statement1"
                              >
                                <a-button :disabled="showDisabled" type="primary"
                                :loading = "loading2"
                                  >点击上传</a-button
                                >
                              </a-upload>
                              <div class="pdf_show" v-if="fujianSource2.length > 0">
                                <div class="pdf_style" v-for="(item, index) in fujianSource2" :key="index" @click="handlePdfView(item)">
                                  <img class="img_icon" v-if="item.name.includes('pdf')" src="@/assets/pdf.png" />
                                  <img class="img_icon" v-if="item.name.includes('ofd')" src="@/assets/ofd.png" />
                                  <div>{{item.name}}</div>
                                  <a-icon class="close_icon" type="close" @click.stop="deteData1(item,index)" />
                                </div>
                              </div>
                            </a-form-model-item>
                          </a-col>
                        </a-row>
                    </div>
<!--                        <div class="title_all">-->
<!--                            &lt;!&ndash; <span class="title_icon"></span>-->
<!--                            <a-collapse-panel-->
<!--                              header="录入人信息"-->
<!--                              class="title_style"-->
<!--                            /> &ndash;&gt;-->
<!--                            <h3 class="active-title">录入人信息</h3>-->
<!--                          </div>-->
<!--                          <div class="active-form">-->
<!--                            <a-row style="margin-top: 15px;">-->
<!--                          <a-col :span="8">-->
<!--                            <a-form-model-item-->
<!--                              label="层级"-->
<!--                              prop="instituLevel"-->
<!--                              :label-col="{ span: 7 }"-->
<!--                              :wrapper-col="{ span: 23 }"-->
<!--                              class="person_info"-->
<!--                            >-->
<!--                                  <a-input-->
<!--                                    class="input_style"-->
<!--                                    v-model="personInfo1"-->
<!--                                    autocomplete="off"-->
<!--                                    placeholder="请输入处理人信息"-->
<!--                                    disabled-->
<!--                                  ></a-input>-->
<!--                            </a-form-model-item>-->
<!--                          </a-col>-->
<!--                          <a-col :span="8">-->
<!--                            <a-form-model-item-->
<!--                              label="机构"-->
<!--                              prop="instituLevel"-->
<!--                              :label-col="{ span: 7 }"-->
<!--                              :wrapper-col="{ span: 23 }"-->
<!--                              class="person_info"-->
<!--                            >-->
<!--                            <a-input-->
<!--                              class="input_style"-->
<!--                              v-model="personInfo2"-->
<!--                              autocomplete="off"-->
<!--                              placeholder="请输入处理人信息"-->
<!--                              disabled-->
<!--                            ></a-input>-->
<!--                            </a-form-model-item>-->
<!--                          </a-col>-->
<!--                          <a-col :span="8">-->
<!--                            <a-form-model-item-->
<!--                              label="姓名"-->
<!--                              prop="instituLevel"-->
<!--                              :label-col="{ span: 7 }"-->
<!--                              :wrapper-col="{ span: 23 }"-->
<!--                              class="person_info"-->
<!--                            >-->
<!--                              <a-input-->
<!--                                class="input_style"-->
<!--                                v-model="personInfo3"-->
<!--                                autocomplete="off"-->
<!--                                placeholder="请输入处理人信息"-->
<!--                                disabled-->
<!--                              ></a-input>-->
<!--                            </a-form-model-item>-->
<!--                          </a-col>-->
<!--                        </a-row>-->
<!--                        </div>-->
                      <!-- </a-col>
                    </a-row> -->
                    <a-row style="margin-top: 15px;">
                      <a-col :span="8" push="10">
                        <a-space>
                          <a-button
                            v-if="!isAudit"
                            v-show="isTitle != '查看'"
                            class="confirm"
                            @click="checkConflict"
                            >录入详情</a-button
                          >
                          <!-- <a-button
                            v-if="isshowIn"
                            class="confirm"
                            @click="SubmitCensorship(false, false)"
                            >保存</a-button
                          > -->
                          <a-button
                            v-show="isshowIn && ruleForm.currStateName != '已终审'"
                            class="confirm"
                            @click="SubmitCensorship(false, true)"
                            >送审</a-button
                          >
                          <!-- <a-button v-if="isTitle == '送审'" class="confirm" @click="shenhe"
                            >送审</a-button
                          > -->
                          <a-button class="confirm" v-if="isTitle == '审核'" @click="shenhe">审核</a-button>
                          <a-button class="confirm" v-show="isTitle == '查看'" @click="lczzjl">流程流转记录</a-button>
                          <a-button @click="close">取消</a-button>
                        </a-space>
                      </a-col>
                    </a-row>
          </a-form-model>
        </a-spin>
    </div>
    <representativeUnit
      ref="representativeUnit"
      @confirm="confirmRepresentativeUnit"
    ></representativeUnit>

    <!-- 受邀范围树重新修改 -->
    <org-db-tree
      ref="identityRelAny"
      default-key="1"
      :levelRoleMainIdentify="levelRoleMainIdentify"
      @saveRel="onAttendMembersSubmit"
    ></org-db-tree>
    <!-- <identity-rel-any-with-db
      ref="identityRelAny"
      :default-key="ruleForm.invitedType"
      @saveRel="saveRel"
    ></identity-rel-any-with-db> -->

    <workerTree ref="ShowworkerTree" orgTreeDialogTitle="选择工作人员" @confirm="confirmworkerTree"></workerTree>
    <conference ref="conference" :show-disabled="showDisabled"></conference>
    <condition
      ref="condition"
      :show-disabled="showDisabled"
      :title-box="titleBox"
      @handleClearId="ifhandleClearId"
    >
    </condition>
    <proposals
      ref="proposals"
      :show-disabled="showDisabled"
      :title-box="titleProposals"
      @handleClearId="ifhandleClearId"
    ></proposals>
    <explanation
      ref="explanation"
      :show-disabled="showDisabled"
      :title-box="titleExplanation"
      @handleClearId="ifhandleClearId"
    ></explanation>
    <!-- 流程弹出页 -->
    <submitForCensorship
      ref="submitForCensorship"
      :proc-inst-id="procInstId"
      :ids="ids"
      @complete="handleComplete"
    >
    </submitForCensorship>
    <!--  -->
    <lczzjl ref="lczzjl" :proc-inst-id="ruleForm.procInstId"></lczzjl>

    <orgTreeDialog
      ref="orgTreeDialog"
      :root-org-id="unit"
      @confirm="confirmRepresentativeUnit"
    ></orgTreeDialog>
    <activityConflictInfo
      ref="activityConflictInfo"
      @submit="handleSubmit"
    ></activityConflictInfo>


    <a-modal v-model="tipVisible" title="提交提醒" @ok="tipVisible = false">
      <p style="color: #f5222d;
      padding:10px;
      background: #fff1f0;
      border-color: #ffa39e;">该送审由代表在i履职移动端补录，请按照实际情况补充表单缺失项后提交</p>
    </a-modal>

  </div>
</template>

<script>
import submitForCensorship from "@/views/common/submitForCensorship";
import Vue from 'vue';
import moment from "moment";
import { getdutyActivecompleteApi } from "@/api/xjgw";
import { instance_1 } from "@/api/axiosRq";
import {
  getLevelList,
  getActivityNatureList,
  getActivityTypeData,
} from "@/api/registrationMage/tableIng.js";
import RepresentativeUnit from "@/views/common/representative/RepresenRadio.vue";
import invitedTree from "@/views/common/representative/invitedTree.vue";
import workerTree from "@/views/common/representative/workerTree.vue";
import conference from "./conference";
import condition from "./condition";
import proposals from "./proposals";
import explanation from "./explanation";
import { myPagination } from "@/mixins/pagination.js";
import lczzjl from "@/views/common/lczzjl";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
import activityConflictInfo from "./activityConflictInfo";
import AnnexDetails from '@/components/AnnexDetails/index';
import ViewTips from '@/components/ViewTips/index';
// import identityRelAnyWithDb from "../../common/identityRelAnyWithDb";
import OrgDbTree from "@/components/OrgDbTree/index.vue";
import MultiLineText from '@/components/MultiLineText'
import { DBLZ_DBLZDJ } from '@/utils/enum/levelRoleMainIdentifyEnum'; // 导入配置文件

export default {
  components: {
    submitForCensorship,
    RepresentativeUnit,
    invitedTree,
    workerTree,
    conference,
    condition,
    proposals,
    explanation,
    lczzjl,
    orgTreeDialog,
    activityConflictInfo,
    AnnexDetails,
    ViewTips,
    // identityRelAnyWithDb,
    OrgDbTree,
    MultiLineText
  },
  mixins: [myPagination],
  data() {
    return {
      levelRoleMainIdentify : DBLZ_DBLZDJ,
      labelText: '组织单位',
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      loading: false,
      isshowIn: false,
      showDisabled: false,
      action: "",
      textBox: "会议",
      action1: "",
      isTitle: "",
      getData: [],
      fileList: [],
      fileList1: [],
      ruleFormData: [],
      titleBox: "会议",
      tipVisible: false,
      columns: [
        {
          title: "文件名",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.fileName || "/";
          },
        },
        {
          title: "上传日期",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.download(record);
                    },
                  },
                },
                "下载"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deteData(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      columns1: [
        {
          title: "文件名",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.fileName || "/";
          },
        },
        {
          title: "上传日期",
          width: 200,
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.createTime || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.download1(record);
                    },
                  },
                },
                "下载"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.deteData1(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
      figure: {
        allAttendNum: null,
        askNum: null,
        inquiryNum: null,
        proposalNum: null,
        realAttendNum: null,
        speakerNum: null,
        suggestNum: null,
      },
      dataSource: null,
      dataSource1: null,
      editShow: false,
      titleBox: "",
      titleProposals: "",
      titleExplanation: "",
      isVisible: false,
      isCondition: false,
      periodList: [] /* 获取届次 */,
      natureData: [],
      typeData: [],
      ruleForm: {
        jcDm: "3" /* 届别 */,
        activityName: "" /* 会议名称 */,
        activityNo: "" /* 会议编号 */,
        activityTypeDm: "" /* 会议类型 */,
        activityTypeName: "" /* 会议类型名称 */,
        activityNatureDm: "" /* 会议性质 */,
        activityNatureName: "" /* 会议性质名称 */,
        address: "" /*  会议地点 */,
        startTime: "" /*  会议开始时间 */,
        endTime: "" /* 会议结束时间   */,
        startTime2: "" /*  会议开始时间 */,
        endTime2: "" /* 会议结束时间   */,
        orgName: "" /* 组织单位 */,
        orgId: "" /* 组织单位 ID*/,
        attendMembers: [] /*  受邀范围 */,
        inviteRangeDesc: "" /*  受邀范围 字符串*/,
        activityWorkers: [] /* 工作人员 */,
        workerDesc: "" /* 工作人员 字符串*/,
        activityContent: "" /* 会议内容 */,
        endTimeRange: "1" /* 结束时间段 */,
        startTimeRange: "1" /* 开始时间段 */,
        invitedType: "",
        activityWorkerInfos: [],
        mannualUnit: '',
      },
      chooseActiveType: 0, // 会议类型判断
      // attendMembers: "",
      activityWorkers: "",
      rules: {
        jcDm: [{ required: true, message: "请输入届别", trigger: "blur" }],
        activityName: [
          { required: true, message: "请输入活动名称", trigger: "blur" },
        ],
        // activityNo: [
        //   { required: true, message: "请输入活动编号", trigger: "blur" },
        // ],
        activityTypeDm: [
          { required: true, message: "请选择会议/活动类型", trigger: "blur" },
        ],
        activityNatureDm: [
          { required: true, message: "请输入活动性质", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请输入活动地点", trigger: "blur" },
        ],
        startTime: [
          { required: true, message: "请输入活动开始时间", trigger: "change" },
        ],
        endTime: [
          { required: true, message: "请输入活动结束时间", trigger: "change" },
        ],
        // orgName: [
        //   { required: true, message: "请输入组织单位", trigger: "blur" },
        // ],
        attendMembers: [
          { required: true, message: "请输入受邀范围", trigger: "blur" },
        ],
        activityWorkers: [
          { required: true, message: "请输入工作人员", trigger: "blur" },
        ],
        inviteRangeDesc: [
          { required: true, message: "请输入受邀范围", trigger: "blur" },
        ],
        workerDesc: [
          { required: true, message: "请输入工作人员", trigger: "blur" },
        ],
        activityContent: [
          { required: true, message: "请输入活动内容", trigger: "blur" },
        ],
      },
      thNumOptions: [],
      conferenceData: {},
      dataSourceLoading: false,
      dataSource1Loading: false,
      iSpinning: false,
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      ids: [],
      procInstId: "",
      isAudit: false,
      isTitle: "",
      areaId: "",
      isRecord: {},
      iShow: null,
      defaultSelect: [], //受邀范围
      fyqkShow: false,
      SubmitCensorshipShow: true,
      unit: "root",
      title: '',
      fujianSource: [],
      fujianSource2: [],
      // personInfo1: '联络站',
      personInfo1: '',
      // personInfo2: '广州市越秀区北京街道中心联络站',
      personInfo2: '',
      // personInfo3: '扬*',
      personInfo3: '',
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
      receiverTreeNodeList: [],
      receiverTreeAllList: [],
      workerTreeNodeList: [],
      loading1: false,
      loading2: false,
    };
  },
  created() {
    let { areaId, title, editShow, titleBox, textBox } = this.$route.query;
    this.title = title
    console.log(this.$route.query, "this.$route.query");
    this.areaId = areaId;
    this.isAudit = this.$route.query.isAudit;
    if (textBox) {
      this.textBox = textBox;
    }
    if (
      title == "参加市人民代表大会" ||
      title == "修改详情" ||
      title == "查看"
    ) {
      this.iShow = true;
      if (title == "查看") {
        this.showDisabled = true;
      }
    } else {
      this.iShow = false;
    }
    if (this.isAudit) {
      this.iSpinning = true;
      this.loading = true;
      this.editShow = true;
      this.isshowIn = true;
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
    }
    if (editShow) {
      this.iSpinning = true;
      this.loading = true;
      this.editShow = true;
      this.isAudit = true;
      this.isshowIn = true;
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
    }
    if (titleBox == "查看所有") {
      this.numberPeople(areaId);
      this.ruleForm.activityId = areaId;
      this.fetchData();
      this.fetchData1();
      this.showDisabled = true;
      this.editShow = true;
    }
    this.action =
      this.GLOBAL.basePath_1 + "/activityReport/upload?activityId=" + areaId;
    this.action1 =
      this.GLOBAL.basePath_1 +
      "/activityAttachment/upload?activityId=" +
      areaId;
    this.$store.dispatch("navigation/breadcrumb1", "登记活动");
    this.$store.dispatch("navigation/breadcrumb2", title);

    // if (title == "修改详情") {
    //   // this.dutyActive(areaId);
    //   this.getLevelListData(title);
    // } else if (title == "送审") {
    //   this.getLevelListData(title);
    //   // this.dutyActive(areaId);
    // } else if (title == "查看") {
    //   this.getLevelListData(title);
    //   // this.dutyActive(areaId);
    // }
    // 判断“发言情况”、“议案建议情况”、“质询案情况”是否显示
    if (
      title == "参加市人民代表大会" ||
      title == "出席市人大常委会会议" ||
      title == "出席市人大常委会主任会议" ||
      title == "列席市人大常委会会议" ||
      title == "列席市人大常委会主任会议" ||
      title == "列席市人大常委会会议并发言"
    ) {
      this.fyqkShow = true;
    } else {
      this.fyqkShow = false;
    }
    if (title == "审核") {
      this.isshowIn = false;
    }
    this.getLevelListData(title);
  },
  computed: {
    // attendMembers() {
    //   if (!this.form.attendMembers) return "";
    //   return this.form.attendMembers.map((it) => it.name || it.userName).join();
    // },
  },
  methods: {
    disabledDateTime() {
      return {
        disabledSeconds: () => {
          var arr = [];
          var j = 60;
          var i = 1;
          for (j = 60; i < j; i++) {
            arr.push(i);
          }
          return arr;
        },
      };
    },
    ifhandleClearId() {
      let id = this.ruleForm.activityId;
      this.numberPeople(id);
    },
    moment,
    // 导入限制 直接return
    drUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
    },
    // 导入限制 直接return
    drUpload1(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
    },
    numberPeople(areaId) {
      instance_1({
        url: "/dutyActive/countInfo",
        method: "get",
        params: { id: areaId || this.areaId },
      }).then((res) => {
        if (res.data.code == "0000") {
          let {
            allAttendNum,
            askNum,
            inquiryNum,
            proposalNum,
            realAttendNum,
            speakerNum,
            suggestNum,
          } = res.data.data;
          this.figure.allAttendNum = allAttendNum;
          this.figure.askNum = askNum;
          this.figure.inquiryNum = inquiryNum;
          this.figure.proposalNum = proposalNum;
          this.figure.realAttendNum = realAttendNum;
          this.figure.speakerNum = speakerNum;
          this.figure.suggestNum = suggestNum;
          this.iSpinning = false;
        }
      });
    },
    download(record) {
      instance_1({
        url: "/activityReport/download",
        method: "get",
        responseType: "blob",
        params: { id: record.id },
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = record.fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    deteData(record, index) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确认删除  ？",
        activityContent: "关联数据也将一并删除，你确定要删除选中项吗",
        onOk: () => {
          instance_1({
            url: "/activityReport/deleteById",
            method: "post",
            // params: { id: record.id },
            params: { id: record.activityId },
          }).then((res) => {
            console.log('删除')
            console.log(res);
            // this.fetchData();
            this.fujianSource.splice(index, 1);
            this.$baseMessage(`删除成功`, "success");
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    download1(record) {
      instance_1({
        url: "/activityAttachment/download",
        method: "get",
        responseType: "blob",
        params: { id: record.id },
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = record.fileName;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    deteData1(record, index) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确认删除  ？",
        activityContent: "关联数据也将一并删除，你确定要删除选中项吗",
        onOk: () => {
          instance_1({
            url: "/activityAttachment/deleteById",
            method: "post",
            // params: { id: record.id },
            params: { id: record.activityId },
          }).then((res) => {
            console.log('删除1')
            console.log(res);
            // this.fetchData1();
            this.fujianSource2.splice(index, 1);
            this.$baseMessage(`删除成功`, "success");
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    //关闭
    close() {
      this.seeTitle = "";
      this.$router.go(-1);
    },
    nationality() {},
    politicalAffiliation() {},
    //  回显
    dutyActive(areaId) {
      instance_1({
        url: "/dutyActive/getById",
        method: "get",
        params: { id: areaId },
      }).then((res) => {
        setTimeout(() => {
          this.loading = false;
        }, 600);
        this.isRecord = res.data.data.attendMembers;
        //获取流程id
        this.isTitle = this.$route.query.title;
        this.ruleForm.procInstId = res.data.data.procInstId;
        this.procInstId = res.data.data.procInstId;
        this.ids.push(res.data.data.id);
        this.conferenceData = res.data.data;
        let {
          jcDm,
          activityName,
          activityNo,
          activityTypeDm,
          activityTypeName,
          activityNatureDm,
          activityNatureName,
          address,
          startTime,
          endTime,
          startTime2,
          endTime2,
          orgName,
          orgId,
          attendMembers,
          inviteRangeDesc,
          activityWorkers,
          workerDesc,
          activityContent,
          currStateName,
          id,
          endTimeRange,
          startTimeRange,
          mannualUnit
        } = res.data.data;
        this.ruleForm.startTimeRange = startTimeRange /* 开始时间段 */;
        this.ruleForm.endTimeRange = endTimeRange /* 结束时间段 */;
        this.ruleForm.id = id /* 届别 */;
        this.ruleForm.jcDm = jcDm /* 届别 */;
        this.ruleForm.activityName = activityName /* 会议名称 */;
        this.ruleForm.activityNo = activityNo /* 会议编号 */;
        this.ruleForm.activityTypeDm = activityTypeDm /* 会议类型代码 */;
        this.ruleForm.activityTypeName = activityTypeName /* 会议类型名称 */;
        this.ruleForm.activityNatureDm = activityNatureDm /* 会议性质代码 */;
        this.ruleForm.activityNatureName =
          activityNatureName /* 会议性质名称 */;
        this.ruleForm.address = address /*  会议地点 */;
        this.ruleForm.startTime = startTime /*  会议开始时间 */;
        this.ruleForm.endTime = endTime /* 会议结束时间   */;
        this.ruleForm.startTime2 = startTime2 /*  会议开始时间 */;
        this.ruleForm.endTime2 = endTime2 /* 会议结束时间   */;
        this.ruleForm.orgName = orgName /* 组织单位 */;
        this.ruleForm.orgId = orgId /* 组织单位 ID*/;
        this.ruleForm.attendMembers = attendMembers /*  受邀范围 */;
        this.ruleForm.inviteRangeDesc = inviteRangeDesc /*  受邀范围 字符串*/;
        this.ruleForm.activityWorkers = activityWorkers /* 工作人员 */;
        this.ruleForm.workerDesc = workerDesc /* 工作人员 字符串*/;
        this.ruleForm.activityContent = activityContent; /* 会议内容 */
        this.ruleForm.currStateName = currStateName; /* 会议内容 */
        this.ruleForm.activityWorkerInfos = this.stringToObjects(mannualUnit);
        // 暂做回显
        // let checkIdentyData = [];
        // let checked = [];
        // if (this.ruleForm.attendMembers.length > 0) {
        //   this.ruleForm.attendMembers.forEach((item) => {
        //     if (item.postId) {
        //       checkIdentyData.push("U" + item.postId);
        //       item.id = "U" + item.postId;
        //       item.fullName = item.userName;
        //       checked.push(item);
        //     } else {
        //       checkIdentyData = [];
        //       checked = [];
        //     }
        //   });
        // }
        // this.$refs.ShowinvitedTree.checkIdentyData = checkIdentyData;
        // this.$refs.ShowinvitedTree.checked = checked;
        // this.$forceUpdate();
        // TODO yu
        // this.workerTreeNodeList = []
        // if (this.ruleForm.activityWorkers.length > 0) {
        //   this.ruleForm.activityWorkers.forEach((item) => {
        //     this.workerTreeNodeList.push(item.id);
        //   });
        // }

        // 判断“发言情况”、“议案建议情况”、“质询案情况”是否显示
        if (
          this.ruleForm.activityTypeName == "参加市人民代表大会" ||
          this.ruleForm.activityTypeName == "出席市人大常委会会议" ||
          this.ruleForm.activityTypeName == "出席市人大常委会主任会议" ||
          this.ruleForm.activityTypeName == "列席市人大常委会会议" ||
          this.ruleForm.activityTypeName == "列席市人大常委会主任会议"
        ) {
          this.fyqkShow = true;
        } else {
          this.fyqkShow = false;
        }
      });
    },
    stringToObjects(str) {
      if (str && str.length>0) {
        const names = str.split(',');
        const objects = [];
        names.forEach(name => {
          objects.push({ name: String(name).trim() });
        });
        return objects;
      }
    },
    //审核
    shenhe() {
      // 新增一个逻辑，如果是代表i履职填写得数据需要校验一下是否选择了活动类型
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          this.ruleForm.attendMembers.forEach((item) => {
            if(!item.userName || item.userName == "") {
              item.userName = item.fullName;
            }
          });
          if (this.ruleForm.activityWorkerInfos && this.ruleForm.activityWorkerInfos.length > 0) {
            let noEmptyNames = this.ruleForm.activityWorkerInfos.filter(item => item.name).map(item => item.name);
            let newUnit = noEmptyNames.join(',');

            if (newUnit && newUnit.endsWith(',')) {
              newUnit = newUnit.slice(0, 1);
            }
           this.ruleForm.mannualUnit = newUnit;
          } else {
           this.ruleForm.mannualUnit = '';
          }
          let url = "/dutyActive/update";
            instance_1({
              url,
              method: "post",
              data: this.ruleForm,
            }).then((res) => {
              if (res.data.code == "0000") {
                this.$refs.submitForCensorship.visible = true;
              } else {
                this.iSpinning = false;
                this.$message.error(res.data.msg);
              }
            });
        } else {
          this.tipVisible = true
        }
      })
    },
    //审核保存发送
    handleComplete(data) {
      this.iSpinning = true
      this.$refs.submitForCensorship.visible = false;
      getdutyActivecompleteApi(data).then((res) => {
        if (res.data.code == "0000") {
          this.$router.push({
            // path: "/registerMange/Handling",
            path: "/registerMange/backlog",
          });
          this.ids = [];
          this.$refs.submitForCensorship.successComplete();
          this.iSpinning = false
          this.$message.success(res.data.msg);
        } else {
          this.iSpinning = false
          this.$message.error(res.data.msg);
        }
      });
    },
    // 上传书面报告按钮
    statement(file) {
      this.loading1 = true;
      setTimeout(() => {
        this.fetchData();
        this.$baseMessage(`添加成功`, "success");
      }, 1000);
    },
    //  开始回显
    fetchData() {
      this.dataSourceLoading = true;
      instance_1({
        url: "/activityReport/getByActivityId",
        method: "get",
        params: { activityId: this.ruleForm.activityId },
      }).then((res) => {
        this.dataSource = res.data.data;
        // this.dataSource = res.data;
        this.fujianSource = this.dataSource.map((item)=>{
          return {
            name: item.fileName,
            // fileUrl: ''
            fileName: item.fileName,
            id: item.id,
             fileUrl: item.pathName + item.physicName,
            activityId: item.activityId
          }
        })
        console.log(this.dataSource,'this.dataSource');
        console.log(this.fujianSource,'this.fujianSource');
        this.dataSourceLoading = false;
        this.loading1 = false;
        this.$refs.conference.selectData(
          this.ruleForm.activityId,
          this.conferenceData,
          this.ruleForm,
          false
        );
        this.$forceUpdate();

        setTimeout(() => {
          this.loading = false;
          this.iSpinning = false;
        }, 1000);
      });
    },
    // 已上传附件
    statement1({ file, fileList }) {
      this.loading2 = true;
      setTimeout(() => {
        this.fetchData1();
        this.$baseMessage(`添加成功`, "success");
      }, 1000);
    },

    // 附件预览
    handlePdfView1(file) {
      let lastChar = file.fileUrl.substring(file.fileUrl.length - 4);
      if(".pdf" == lastChar || ".PDF" == lastChar){

          let pdfUrl =
        Vue.prototype.GLOBAL.basePath_1 +
        "/file/view?file=" +
        file.fileUrl +
        "&token=" +
        Vue.prototype.GLOBAL.token;
         window.open("/pdfjs/web/viewer.html?file=" + encodeURIComponent(pdfUrl));

      }else{

         this.$message.success("文件不支持预览，正在下载文件，请稍后");
         this.loading = true;
         instance_1({
            url: "/activityReport/download",
            method: "get",
            responseType: "blob",
            params: { id: file.id },
          }).then((res) => {
            let a = window.document.createElement("a");
            res = URL.createObjectURL(res.data);
            a.href = res;
            a.download = file.fileName;
            window.document.body.appendChild(a);
            a.click();
            window.document.body.removeChild(a);
          });
          this.loading = false;
      }
    },

    // 附件预览
    handlePdfView(file) {
      let lastChar = file.fileUrl.substring(file.fileUrl.length - 4);
      if(".pdf" == lastChar || ".PDF" == lastChar){
          let pdfUrl =
        Vue.prototype.GLOBAL.basePath_1 +
        "/file/view?file=" +
        file.fileUrl +
        "&token=" +
        Vue.prototype.GLOBAL.token;
         window.open("/pdfjs/web/viewer.html?file=" + encodeURIComponent(pdfUrl));

      }else{
          this.$message.success("文件不支持预览，正在下载文件，请稍后");
          this.loading = true;
            instance_1({
            url: "/activityAttachment/download",
            method: "get",
            responseType: "blob",
            params: { id: file.id },
          }).then((res) => {
            let a = window.document.createElement("a");
            res = URL.createObjectURL(res.data);
            a.href = res;
            a.download = file.fileName;
            window.document.body.appendChild(a);
            a.click();
            window.document.body.removeChild(a);
          });
          this.loading = false;
      }

    },
    //  开始回显
    fetchData1() {
      this.dataSource1Loading = true;
      instance_1({
        url: "/activityAttachment/getByActivityId",
        method: "get",
        params: { activityId: this.ruleForm.activityId },
      }).then((res) => {
        console.log(res.data);
        this.dataSource1 = res.data.data;
        // this.dataSource1 = res.data;
        this.fujianSource2 = this.dataSource1.map((item)=>{
          return {
            name: item.fileName,
            // fileUrl: ''
            fileName: item.fileName,
            id: item.id,
            fileUrl: item.pathName + item.physicName,
            activityId: item.activityId
          }
        })
        console.log(this.fujianSource,'this.fujianSource');
        this.dataSource1Loading = false;
        this.loading2 = false;
      });
    },
    accessory(file, fileList) {
      console.log(file);
      console.log(fileList);
    },
    //判断开始时间和结束时间是否为同一天
    isSameDay(startTimeStr, endTimeStr) {
      // 将时间字符串解析为Date对象
      const startTime = this.parseDateTime(startTimeStr);
      const endTime = this.parseDateTime(endTimeStr);
      // 比较年份、月份和日期
      return (
        startTime.getFullYear() === endTime.getFullYear() &&
        startTime.getMonth() === endTime.getMonth() &&
        startTime.getDate() === endTime.getDate()
      );
    },
    // 辅助函数，用于解析时间字符串为Date对象
    // 支持"YYYY-MM-DD"和"YYYY-MM-DD HH:mm:ss"格式
    parseDateTime(dateTimeStr) {
      // 尝试按"YYYY-MM-DD HH:mm:ss"格式解析
      const date = new Date(dateTimeStr);
      // 如果解析后的日期是无效的（例如，NaN），则尝试按"YYYY-MM-DD"格式重新解析
      // 注意：这里我们检查date是否为无效日期的一种简单方式是看它的时间戳是否为NaN
      if (isNaN(date.getTime())) {
        // 尝试去除时间部分后重新解析
        const dateOnlyStr = dateTimeStr.split(' ')[0]; // 假设时间部分总是跟在日期后面并用空格分隔
        return new Date(dateOnlyStr);
      }
      return date;
    },
    //判断开始时间是否晚于结束时间
    validDate() {
      if (this.chooseActiveType == '1') {
        if (this.isSameDay(this.ruleForm.startTime, this.ruleForm.endTime)) {
          if ((this.ruleForm.startTime = this.ruleForm.endTime) && (this.ruleForm.startTimeRange > this.ruleForm.endTimeRange)) {
            return true;
          }
          if ((this.ruleForm.startTime != this.ruleForm.endTime) && (this.checkDate(this.ruleForm.startTime) > this.checkDate(this.ruleForm.endTime))) {
            return true;
          }
        } else if (!this.isSameDay(this.ruleForm.startTime, this.ruleForm.endTime)) {
          if (this.ruleForm.startTime > this.ruleForm.endTime) {
            return true;
          }
        }
      } else {
        if (this.ruleForm.startTime > this.ruleForm.endTime) {
          return true;
        }
      }
    },

    //录入详情
    handleSubmit() {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
        //   if (this.ruleForm.startTime > this.ruleForm.endTime) {
        //     this.$message.error("当前开始时间晚于结束时间，请您重新输入");
        //   }
          if(this.validDate()){
            this.$message.error("当前开始时间晚于结束时间，请您重新输入");
          } else {
            // 加载中
            this.iSpinning = true;
            this.isAudit = true;
            for (let index = 0; index < this.typeData.length; index++) {
              const element = this.typeData[index];
              if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                this.ruleForm.activityTypeName = element.activityTypeName;
                break;
              }
            }
            for (let index = 0; index < this.natureData.length; index++) {
              const element = this.natureData[index];
              if (element.activityNatureDm == this.ruleForm.activityNatureDm) {
                this.ruleForm.activityNatureName = element.activityNatureName;
                break;
              }
            }
            let url = "";
            url = "/dutyActive/create";
            if (this.chooseActiveType === 0) {
              this.ruleForm.startTimeRange = this.checkDate(this.ruleForm.startTime)
              this.ruleForm.endTimeRange = this.checkDate(this.ruleForm.endTime)
            }
            if (this.chooseActiveType === 1) {
              this.ruleForm.startTime = this.ruleForm.startTime.split(' ')[0].concat(' 00:00:00')
              this.ruleForm.endTime = this.ruleForm.endTime.split(' ')[0].concat(' 00:00:00')
            }
            if (this.ruleForm.activityWorkerInfos && this.ruleForm.activityWorkerInfos.length > 0) {
              let noEmptyNames = this.ruleForm.activityWorkerInfos.filter(item => item.name).map(item => item.name);
              let newUnit = noEmptyNames.join(',');

              if (newUnit && newUnit.endsWith(',')) {
                newUnit = newUnit.slice(0, 1);
              }
              this.ruleForm.mannualUnit = newUnit;
            } else {
              this.ruleForm.mannualUnit = '';
            }
            instance_1({
              url,
              method: "post",
              data: this.ruleForm,
            }).then((res) => {
              if (res.data.code == "0000") {
                this.ruleFormData = res.data.data;
                this.areaId = this.ruleFormData.id;
                this.$message.success("添加成功");
                this.iSpinning = false;
                this.isAudit = true;
                this.isshowIn = true;
                this.editShow = true;
                this.isRecord = res.data.data.attendMembers;
                this.conferenceData = res.data.data;
                this.ruleForm.id = res.data.data.id;
                this.ruleForm.activityId = res.data.data.id;
                this.action =
                  this.GLOBAL.basePath_1 +
                  "/activityReport/upload?activityId=" +
                  this.ruleForm.activityId;
                this.action1 =
                  this.GLOBAL.basePath_1 +
                  "/activityAttachment/upload?activityId=" +
                  this.ruleForm.activityId;
                this.$nextTick(() => {
                  this.numberPeople(this.ruleForm.id);
                  console.log(
                    "🤗🤗🤗, textBox =>",
                    this.ruleForm.activityTypeName
                  );
                  if (this.ruleForm.activityTypeName == "参加市人民代表大会") {
                    this.fyqkShow = true;
                  }
                });
                this.ruleForm.procInstId = res.data.data.procInstId;

                // this.ids.push(res.data.data.id);
                // this.$router.go(-1);
                // this.$refs.submitForCensorship.visible = true;
              } else {
                this.iSpinning = false;
                this.isAudit = false;
                this.$message.error(res.data.msg);
              }
            });
          }
        }
      });
    },
    // 判断时间是上午还是下午
    checkDate(date) {
      const newDate = new Date(date)
      const hour = newDate.getHours();
      if (hour >= 12) {
        return '2';
      } else {
        return '1';
      }
    },
    //检查是否有录入冲突
    checkConflict() {
     let allEmpty = true; // 假设所有项开始都是空的
      this.ruleForm.activityWorkerInfos.forEach(item => {
        if (item.name !== '') { // 如果找到一个非空项
          allEmpty = false; // 标记为非所有项都为空
          return; // 退出循环，因为已经找到非空项，无需继续检查
        }
      });
      if (allEmpty && this.ruleForm.orgName === '') { // 如果循环结束后，allEmpty仍为true，说明所有项都为空
        return this.$message.warning('请选择组织单位或填写组织单位姓名！');
      }
      this.$refs.activityConflictInfo.checkActivityConflict(this.ruleForm);
    },
    //保存并送审
    SubmitCensorship(state, isShow) {
      this.$refs["ruleForm"].validate((valid) => {
        if (valid) {
          // if (this.ruleForm.startTime > this.ruleForm.endTime) {
          //   this.$message.error("当前开始时间晚于结束时间，请您重新输入");
          // }
          if(this.validDate()){
            this.$message.error("当前开始时间晚于结束时间，请您重新输入");
          } else {
            this.iSpinning = true;
            // if(this.SubmitCensorshipShow){
            if (
              this.$route.query.title == "修改详情" ||
              this.$route.query.title == "送审" ||
              this.$route.query.title == "审核" ||
              state
            ) {
              // this.ruleForm.attendMembers.forEach((item) => {
              //   item.userName = item.fullName;
              // });
              for (let index = 0; index < this.typeData.length; index++) {
                const element = this.typeData[index];
                if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                  this.ruleForm.activityTypeName = element.activityTypeName;
                  break;
                }
              }
              for (let index = 0; index < this.natureData.length; index++) {
                const element = this.natureData[index];
                if (
                  element.activityNatureDm == this.ruleForm.activityNatureDm
                ) {
                  this.ruleForm.activityNatureName = element.activityNatureName;
                  break;
                }
              }
              // this.ruleForm.attendMembers.forEach((item) => {
              //   item.userName = item.fullName;
              // });
              // this.ruleForm.attendMembers.forEach((item) => {
              //   item.userName = item.fullName;
              // });
              for (let index = 0; index < this.typeData.length; index++) {
                const element = this.typeData[index];
                if (element.activityTypeDm == this.ruleForm.activityTypeDm) {
                  this.ruleForm.activityTypeName = element.activityTypeName;
                  break;
                }
              }
              for (let index = 0; index < this.natureData.length; index++) {
                const element = this.natureData[index];
                if (
                  element.activityNatureDm == this.ruleForm.activityNatureDm
                ) {
                  this.ruleForm.activityNatureName = element.activityNatureName;
                  break;
                }
              }
              // this.ruleForm.attendMembers.forEach((item) => {
              //   item.userName = item.fullName;
              // });
              if (this.ruleForm.activityWorkerInfos && this.ruleForm.activityWorkerInfos.length > 0) {
                let noEmptyNames = this.ruleForm.activityWorkerInfos.filter(item => item.name).map(item => item.name);
                let newUnit = noEmptyNames.join(',');

                if (newUnit && newUnit.endsWith(',')) {
                  newUnit = newUnit.slice(0, 1);
                }
                this.ruleForm.mannualUnit = newUnit;
              } else {
                this.ruleForm.mannualUnit = '';
              }
              this.ruleFormData = this.ruleForm;
            }

            let url = "/dutyActive/update";
            instance_1({
              url,
              method: "post",
              data: this.ruleFormData,
            }).then((res) => {
              if (res.data.code == "0000") {
                if (isShow) {
                  this.$refs.submitForCensorship.visible = true;
                }
                this.procInstId = res.data.data.procInstId;
                this.ids.push(res.data.data.id);
                let id = res.data.data.id;
                this.numberPeople(id);
              } else {
                this.iSpinning = false;
                this.$message.error(res.data.msg);
              }
            });
          }
        }
        // }
      });
    },
    getLevelListData(title) {
      // 获取会议性质
      let getActivityNatureListData = () => {
        getActivityNatureList().then((res) => {
          if (res.data.code == "0000") {
            this.natureData = res.data.data;
            // 根据id获取回显数据
            // 编辑才回显
            if (this.editShow || title == "查看") {
              this.dutyActive(this.areaId);
            }
          } else {
            this.$message.error(res.data.message);
          }
        });
      };

      // 获取届次
      let getLevelListData = () => {
        getLevelList({}).then((res) => {
          if (res.data.code == "0000") {
            this.periodList = res.data.data;
            // 新增
            if (!this.editShow) {
              this.typeData.forEach((item) => {
                if (item.activityTypeDm == this.$route.query.code) {
                  this.ruleForm.activityTypeDm = item.activityTypeDm;
                }
              });
            }
            getActivityNatureListData();
          } else {
            this.$message.error(res.data.message);
          }
        });
      };

      // 获取会议类型
      getActivityTypeData().then((res) => {
        if (res.data.code == "0000") {
          // 新增
          if (!this.editShow) {
            res.data.data.forEach((item) => {
              if (title == item.activityTypeName) {
                this.ruleForm.activityTypeDm =
                  item.activityTypeDm /* 会议类型代码 */;
              }
            });
          }
          this.typeData = res.data.data;

          getLevelListData();
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    openUnit() {
      this.$refs.representativeUnit.orgTreeDialogVisible = true;
      this.$refs.representativeUnit.orgTreeDialogTitle = "选择组织单位";
    },
    // 机构选择器
    handlePublish() {
      this.$refs.orgTreeDialog.open();
    },
    addRow(){
      this.ruleForm.activityWorkerInfos = this.ruleForm.activityWorkerInfos || [];
      this.ruleForm.activityWorkerInfos.push({
        name: '',
      });
      this.$forceUpdate()
    },
    jianjianJian1(index) {
      this.ruleForm.activityWorkerInfos.splice(index, 1);
      this.$forceUpdate()
    },
    openattendMembers() {
      // this.$refs.ShowinvitedTree.newOrgTreeDialogVisible = true;
      // this.$refs.ShowinvitedTree.orgTreeDialogTitle = "选择受邀范围";
    },
    handleRel() {
      this.$refs["identityRelAny"].defaultKey = this.ruleForm.invitedType == "0" ? "1" : "2";
      if (this.ruleForm.invitedType == 1) {
        this.$refs["identityRelAny"].handleShow(0, this.ruleForm.userGroupId
        );
      } else {
        this.$refs["identityRelAny"].handleShow(1,this.receiverTreeNodeList, this.receiverTreeAllList);
      }
    },
    openJoinTable() {
      this.$refs.ShowworkerTree.newOrgTreeDialogVisible = true;
      this.$refs.ShowworkerTree.handleShow(this.workerTreeNodeList)
      // this.$refs.ShowworkerTree.orgTreeDialogTitle = "选择工作人员";
    },
    confirmRepresentativeUnit(data) {
      if (data) {
        this.ruleForm.orgName = (data[0].parentName ? (data[0].parentName + '-') : '') + data[0].orgName;
        this.ruleForm.orgId = data[0].orgId;
      }
    },
    // confirminvitedTree(data) {
    //   if (data) {
    //     this.defaultSelect = data;
    //     this.ruleForm.attendMembers = [];
    //     this.ruleForm.inviteRangeDesc = "";
    //     this.ruleForm.attendMembers = this.recursionChild(data);
    //     this.ruleForm.attendMembers.forEach((item, index) => {
    //       if (index !== this.ruleForm.attendMembers.length - 1) {
    //         this.ruleForm.inviteRangeDesc =
    //           this.ruleForm.inviteRangeDesc + item.fullName + "、";
    //       } else {
    //         this.ruleForm.inviteRangeDesc =
    //           this.ruleForm.inviteRangeDesc + item.fullName;
    //       }
    //     });
    //     if (this.isshowIn) {
    //       //  如果新增后再改变自动修改一下
    //       this.$nextTick(() => {
    //         this.SubmitCensorship(true);
    //       });
    //     }
    //   }
    // },
    confirmworkerTree(checkedData) {
      // this.workerTreeNodeList = []
      // if (data) {
        // this.ruleForm.activityWorkers = [];
        // this.ruleForm.workerDesc = "";
        // this.ruleForm.activityWorkers = data
        // this.ruleForm.activityWorkers.forEach((item, index) => {
        //   if (index !== this.ruleForm.activityWorkers.length - 1) {
        //     this.ruleForm.workerDesc =
        //       this.ruleForm.workerDesc + item.fullName + "、";
        //   } else {
        //     this.ruleForm.workerDesc = this.ruleForm.workerDesc + item.fullName;
        //   }
        //   this.workerTreeNodeList.push(item.id)
        // });
      // }
      let workerList = [];
      //用户树选择赋值
      if (checkedData.length > 0) {
        //遍历返回的身份id列表
        checkedData.forEach((treeNode) => {
          workerList.push({
            id: treeNode.postId,
            userId: treeNode.userId,
            userName: treeNode.fullName,
            orgId: treeNode.orgId,
            orgName: treeNode.orgName,
            deptId: treeNode.deptId,
            deptName: treeNode.deptName,
            postId: treeNode.postId,
            postName: treeNode.postName,
          });
        });
      }
      this.ruleForm.activityWorkers = workerList
    },
    recursionChild(arr) {
      console.log('进入了recursionChild方法');

      var res = [];
      arr.map((item) => {
        if (item.children && Array.isArray(item.children)) {
          // res = res.concat(this.recursionChild(item.children));
        } else {
          res.push(item);
        }
      });
      return res;
    },
    attendance() {
      this.$refs.conference.selectData(
        this.ruleForm.activityId,
        this.conferenceData,
        this.ruleForm,
        true
      );
    },
    condition(titleBox) {
      this.titleBox = titleBox;
      this.$refs.condition.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },
    proposals(titleBox) {
      this.titleProposals = titleBox;
      this.$refs.proposals.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },
    explanation(titleBox) {
      this.titleExplanation = titleBox;
      this.$refs.explanation.fetchData1(
        this.ruleForm.activityId,
        this.ruleForm,
        this.conferenceData
      );
    },

    handleCancel() {
      this.isVisible = false;
    },
    isConditionCancel() {
      this.isCondition = false;
    },
    formatter(value) {
      if (value == "0") {
        return "";
      } else if (value < 10) {
        return "0" + value + ":00";
      } else {
        return value + ":00";
      }
    },
    lczzjl() {
      this.$refs.lczzjl.visible = true;
    },
    onAcitveDmChange() {
      // 18：其他活动：去掉时间选项
      if (this.ruleForm.activityTypeDm === '18') {
        this.chooseActiveType = 1
      } else {
        // 去掉上下午
        this.chooseActiveType = 0
      }
    },
    saveRel(type, checkedData) {
      //清空原有的接收范围
      this.receiverIdentityList = [];
      this.receiverTreeNodeList = [];
      this.receiverTreeAllList = [];
      let relName = "";
      let beInvited = [];

      //用户树选择赋值
      if (type == "1") {
        if (checkedData.length > 0) {
          this.ruleForm.invitedType = "0";
          //遍历返回的身份id列表
          checkedData.forEach((treeNode) => {
            if (treeNode.itemType == "2") {
                beInvited.push({
                  userId: treeNode.id,
                  // inviteId: treeNode.uniqueId,
                  dhDm: treeNode.dhDm,
                  jcDm: treeNode.jcDm,
                  sfDm: treeNode.sfDm,
                  orgId: treeNode.orgId,
                  deptId: treeNode.deptId,
                  postId: treeNode.postId,
                  orgName: treeNode.orgName,
                  deptName: treeNode.deptName,
                  postName: treeNode.postName,
                  userName: treeNode.name
                });
                this.receiverTreeNodeList.push(treeNode.postId);
                this.receiverTreeAllList.push(treeNode);
            }
          });
          this.ruleForm.attendMembers = beInvited
        }
      }

      if (type == "2") {
        let userGroupId = [];
        this.ruleForm.invitedType = "1";
        checkedData.forEach((treeNode) => {
          relName += treeNode.name + ",";
          userGroupId.push(treeNode.id);
        });
        this.ruleForm.userGroupId = userGroupId.toString();
        this.ruleForm.userGroupName = relName.substring(0, relName.length - 1);
      }
    },
    onAttendMembersSubmit(type, checkedData) {
      //用户树选择赋值
      if (type == "1") {
        const list = checkedData
          .map((it) => ({
            userId: it.id,
            inviteId: it.uniqueId,
            dhDm: it.dhDm,
            jcDm: it.jcDm,
            orgId: it.orgId,
            deptId: it.deptId,
            postId: it.postId,
            orgName: it.orgName,
            deptName: it.deptName,
            postName: it.postName,
            name: it.name,
            userName: it.userName,
            sfDm: it.sfDm,
            committeeMemberTypeId: it.sfDm,
          }));
        this.$set(this.ruleForm, "attendMembers", list);
      }
    },
    onAttendMembers() {
      this.$refs["identityRelAny"] &&
        this.$refs["identityRelAny"].handleShow(
          "1",
          this.ruleForm.attendMembers
        );
    },
  },
  watch: {
    'ruleForm.activityTypeDm': 'onAcitveDmChange',
    "ruleForm.attendMembers": {
      handler: function (newVal) {
        let relName = ""
        this.receiverTreeNodeList = [];
        this.receiverTreeAllList = [];
        newVal.forEach((item) => {
          relName += (item.name || item.userName)  + ",";
          this.receiverTreeNodeList.push(item.postId);
          this.receiverTreeAllList.push(item);
        });
        this.$set(this.ruleForm, 'inviteRangeDesc',  relName.substring(0, relName.length - 1))
      },
      deep: true,
      immediate: true
    },
    "ruleForm.activityWorkers": {
      handler: function (newVal) {
        let relName = ""
        this.workerTreeNodeList = [];
        newVal.forEach((item) => {
          relName += (item.userName || item.fullName)  + ",";
          this.workerTreeNodeList.push(item.postId);
        });
        this.$set(this.ruleForm, 'workerDesc',  relName.substring(0, relName.length - 1))
      },
      deep: true,
      immediate: true
    }
  }
};
</script>

<style lang="scss" scoped>
.table-container {
  padding: 0 20px 50px 20px!important;
}
.table-title{
  font-size: 16px;
  font-weight: 700;
}
// ::v-deep .ant-spin-nested-loading {
//   padding: 0 20px 20px 20px !important;
// }
::v-deep .table-container {
  padding-bottom: 0;
  margin-bottom: -60px;
}
.table-container {
  padding-top: 0;
}
.title_all {
   position: relative;
  //  margin-left: -15px;
   margin-left: 0;
   margin-right: -15px;

  .title_style {
    padding: 10px 0px 0 10px;
    background: rgba(190, 64, 61, 0.1);
    padding-bottom: 12px;
    border-bottom: 1px solid #cfcece;
  }
  .title_icon {
    position: absolute;
    top: 50%;
    transform: translate(0, -6px);
    display: inline-block;
    width: 6px;
    height: 10px;
    background-color: #d92b3e;
  }
}
.pdf_show {
  display: flex;
  flex-wrap: wrap;
  margin: 20px 0;
  .pdf_style {
    position: relative;
    // display: flex;
    // align-items: center;
    margin-right: 50px;
    text-align: center;
    .img_icon {
      width: 40px;
      height: fit-content;
      margin-right: 10px;
    }
    .close_icon {
      position: absolute;
      top: -5px;
      right: 0;
      cursor: pointer;
    }
  }
}
.info_style {
  display: flex;
  align-items: center;
  >span {
    flex: 1.5;
  }
  .input_style {
    flex: 8.5;
  }
}
.footer_sytle {
  margin-top: 20px;
  margin-bottom: -100px;
}
::v-deep .ant-input[disabled] {
  background-color: #f7f7f9!important;
  background: #f7f7f9!important;
  // border-color: #f7f7f9!important;
}
::v-deep .ant-form-item-label {
  text-align: left;
}
::v-deep .ant-form-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 0;
}
.confirm {
  background-color: #d92b3e;
  color: #fff;
}

.confirm:hover {
  border-color: #d92b3e !important;
}

.register-box {
  .Steps {
    width: 80%;
    margin: 0 auto;
    margin-top: 10px;
    margin-bottom: 10px;
  }
}

.table {
  width: 780px;
  // margin-left: -158px;
}

.active-title {
    margin-top: 20px;
    height: 30px;
    line-height: 30px;
    border-left: 4px solid #c71c33;
    padding-left: 18px;
    font-weight: bold;
    font-size: 18px;
    box-sizing: border-box;
  }

.active-content {
  .active-form {
    border-radius: 8px;
    border: 1px solid #dcdfe6;
    // padding: 20px 0;
    padding: 20px;
  }
}

// 下拉框 禁用状态下 文字样式
::v-deep .ant-select-disabled .ant-select-selection {
    // background: #f5f5f5;
    background: #f7f7f9;
    cursor: not-allowed;
    color: rgba(0, 0, 0, 0.65) !important;
}

.flex_da,
.daxin {
  display: flex;
}

.daxin {
  flex-direction: column;
}

.daxin_i {
  display: flex;
  margin-top: 10px;
}
</style>
