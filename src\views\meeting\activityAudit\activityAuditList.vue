<template>
  <div class="table">
    <standard-table :pagination="pagination"
                    rowKey="id"
                    :columns="columns"
                    :loading="TBloading"
                    :dataSource="list"></standard-table>
  </div>
</template>
<script>
import { myPagination } from "@/mixins/pagination.js";
import { firstAudit as backlogValue } from "@/api/area.js";
import { instance_1 } from "@/api/axiosRq";
export default {
  mixins: [myPagination],
  data () {
    return {
      TBloading: false,
      queryForm: {
        pageNum: 1,
        pageSize: 10,
      },
      list: [],
      columns: [
        {
          title: "当前状态",
          align: "center",
          width: 180,
          ellipsis: true,
          dataIndex: "currentState.currStateName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动类型",
          width: 180,
          ellipsis: true,
          dataIndex: "activityTypeName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动编号",
          width: 180,
          ellipsis: true,
          dataIndex: "activityNo",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动名称",
          ellipsis: true,
          dataIndex: "activityName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动内容",
          ellipsis: true,
          dataIndex: "activityContent",
          ellipsis: true,
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "活动时间",
          ellipsis: true,
          dataIndex: "startTime",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "参加人数",
          ellipsis: true,
          dataIndex: "attendNum",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "录入人",
          ellipsis: true,
          dataIndex: "creatorName",
          customRender: (text, record, index) => {
            if (text) {
              return text;
            } else {
              return "/";
            }
          },
        },
        {
          align: "center",
          title: "录入日期",
          ellipsis: true,
          dataIndex: "createTime",
          customRender: (text, record, index) => {
            if (text) {
              return text.replace("T", " ").split("Z").join("").substr(0, 19);
            } else {
              return "/";
            }
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 180,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            return h("div", [
              // h(
              //   "span",
              //   {
              //     attrs: {
              //       type: "text",
              //     },
              //     style: {
              //       cursor: "pointer",
              //       marginLeft: "14px",
              //       color: "#DB3046",
              //     },
              //     on: {
              //       click: () => {
              //         // this.switMeeting(record);
              //       },
              //     },
              //   },
              //   "送审"
              // ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.editData(record);
                    },
                  },
                },
                "修改"
              ),
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.handleDelete(record);
                    },
                  },
                },
                "删除"
              ),
            ]);
          },
        },
      ],
    };
  },
  created () {
    let queryForm = JSON.parse(localStorage.getItem("tongjifenzi"));
    if (queryForm) {
      this.queryForm = queryForm
      this.pagination.current = this.queryForm.pageNum
    }
    this.$store.dispatch("navigation/breadcrumb1", "登记管理");
    this.$store.dispatch("navigation/breadcrumb2", "待办事项");
    this.fetchData();
  },
  beforeDestroy () {
    window.localStorage.setItem("tongjifenzi", JSON.stringify(this.queryForm));
  },
  methods: {
    // 删除
    handleDelete (row) {
      console.log(row, "row");
      if (row.id) {
        this.$baseConfirm("你确定要删除当前项吗", null, () => {
          instance_1({
            method: "post",
            url: "/dutyActive/delete",
            params: {
              id: row.id,
            },
          }).then((res) => {
            if (res.data.code == "0000") {
              this.$message.success("删除成功");
              this.fetchData();
            } else {
              this.$message.error("删除失败");
            }
          });
        });
      }
    },
    async fetchData () {
      this.TBloading = true;
      let res = await backlogValue(this.queryForm);
      if (res.code == 200) {
        res.rows.forEach((item) => {
          if (item.user_name) {
            let str = item.user_name;
            let len = item.user_name.replace(/[^\x00-\xff]/g, "01").length;
            if (len >= 40) {
              item.user_name = str.substr(0, 40) + "...";
            } else {
              item.user_name = str;
            }
          }
        });
        this.list = res.rows;
        this.pagination.total = res.total;
        this.TBloading = false;
      }
    },
    // 编辑
    editData (record) {
      this.$router.push({
        path: "/delegate/addRegister",
        query: { areaId: record.id, title: "修改详情", editShow: true },
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.table {
  padding: 15px;
}
</style>
