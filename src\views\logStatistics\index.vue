<template>
  <div class="table-container">
    <a-row>
      <a-form :model="queryForm" layout="horizontal">
        <div>
          <a-row>
            <a-col :xs="24" :sm="24" :md="5" :lg="6" :xl="5">
              <a-form-model-item
                label="统计范围"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 17, offset: 1 }"
                prop="state"
              >
                <a-select
                  v-model="queryForm.queryType"
                  placeholder="选择模块"
                  allow-clear
                  filterable
                  default-first-option
                  @change="queryTypeChange"
                >
                  <a-select-option
                    v-for="(item, index) in queryTypeOptions"
                    :key="index"
                    :label="item.label"
                    :value="item.value"
                  >
                    {{ item.label }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="5" :lg="6" :xl="5">
              <a-form-model-item
                label="开始时间"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 17, offset: 1 }"
                prop="startTime"
              >
                <a-date-picker
                  allow-clear
                  v-model="queryForm.startTime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  show-time
                  placeholder="选择开始时间"
                  :disabled-date="
                    (current) =>
                      current && queryForm.endTime
                        ? current.valueOf() >=
                          moment(new Date(queryForm.endTime)).valueOf()
                        : false
                  "
                ></a-date-picker>
              </a-form-model-item>
            </a-col>
            <a-col :xs="24" :sm="24" :md="5" :lg="6" :xl="5">
              <a-form-model-item
                label="结束时间"
                :label-col="{ span: 4 }"
                :wrapper-col="{ span: 17, offset: 1 }"
                prop="endTime"
              >
                <a-date-picker
                  allow-clear
                  v-model="queryForm.endTime"
                  show-time
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="选择结束时间"
                  :disabled-date="
                    (current) =>
                      current && queryForm.startTime
                        ? moment(new Date(queryForm.startTime)).valueOf() >=
                          current.valueOf()
                        : false
                  "
                ></a-date-picker>
              </a-form-model-item>
            </a-col>
          </a-row>
        </div>
      </a-form>
      <!--  -->
    </a-row>
    <!-- 操作 -->
    <a-spin :indicator="indicator" :spinning="listLoading">
      <div class="zhuBox" v-if="zhuData.dbBind && !listLoading">
        <div class="data">
          <div style="color: #d53132;" @click="skiurl()">
            {{ zhuData.dbBind }}个
            <div style="color: #323233;">
              代表已注册
            </div>
          </div>
        </div>
      </div>
      <div class="ehArts-box">
        <div class="ttbox">
          <div class="ehArts" id="data" @click="clickArts"></div>
          <div class="dw">单位：次</div>
          <div class="beizhu">
            备注：以上为用户点击率统计，用户每点击一次统计值加1。
          </div>
        </div>
        <div class="ttbox">
          <div
            class="ehArts"
            id="landing"
            style="margin-bottom: 1.8rem;"
            @click="clickArts"
          ></div>
          <div class="dw">单位：人次</div>
        </div>
        <!-- <div class="ehArts"
           id="worker"></div> -->
        <div class="ttbox">
          <div class="ehArts" id="behalf"></div>
          <div class="dw">单位：人次</div>
          <div class="beizhu">
            备注：以上为用户访问各栏目情况，用户每点击一次统计值加1。
          </div>
        </div>
        <div class="ttbox">
          <div class="ehArts" id="getColumn"></div>
        </div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import checkPermission from "@/utils/permission";
import moment from "moment";

// 引入基本模板
let echarts = require("echarts/lib/echarts");

// 引入提示框和title组件
require("echarts/lib/component/tooltip");
require("echarts/lib/component/title");
require("echarts/lib/component/legend");
// 引入饼状图组件
require("echarts/lib/chart/pie");
// 引入柱状图组件
require("echarts/lib/chart/bar");
// 引入柱状图组件
// 折线
require("echarts/lib/chart/line");

export default {
  name: "Table",
  components: {},
  filters: {},
  data() {
    return {
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      advanced: false,
      zhuData: {
        dbBind: null,
        dbNoBind: null,
      },
      queryForm: {
        queryType: "year",
        userType: "0",
        startTime: moment(new Date()).format("YYYY"),
      },
      queryTypeOptions: [
        { label: "年", value: "year" },
        { label: "月", value: "month" },
        { label: "区间", value: "interval" },
      ],
    };
  },
  computed: {},
  watch: {},
  created() {
    this.getChartApi();
  },
  mounted() {},

  methods: {
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    handleQuery() {
      this.getChartApi();
    },
    clickArts() {},

    queryTypeChange(value) {
      if (value == "year") {
        this.dateType = "year";
        this.queryForm.queryType = "year";
        this.queryForm.startTime = moment(this.queryForm.startTime).format(
          "YYYY"
        );
        this.queryForm.endTime = "";
      } else if (value == "month") {
        this.dateType = "date";
        this.queryForm.queryType = "month";
        this.queryForm.startTime = moment(new Date(new Date())).format(
          "YYYY-MM"
        );
        this.queryForm.endTime = "";
      } else {
        this.queryForm.queryType = "interval";
        this.queryForm.startTime = moment(this.queryForm.startTime).format(
          "YYYY-MM-DD"
        );
      }
      this.getChartApi();
    },

    getChartApi() {
      // 1折线图
      instance_1({
        method: "get",
        url: "/common/log/getShiyongTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getChart(res.data.data);
        }
      });
      let { queryType, userType } = this.queryForm;
      // 注册人员
      instance_1({
        method: "get",
        url: "/common/log/getZhuceTotal",
        params: {
          queryType: queryType,
          startTime: "",
          endTime: "",
          userType: userType,
        },
      }).then((res) => {
        if (res.data.code == "0000") {
          this.zhuData = res.data.data;
        }
      });

      // 用户登录情况
      instance_1({
        method: "get",
        url: "/common/log/getDengluTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getDeng(res.data.data);
        }
      });
      // 工作人员使用情况
      instance_1({
        method: "get",
        url: "/common/log/getMoudleTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          // this.getShi(res.data.data)
          this.getDai(res.data.data);
        }
      });

      // 各栏目使用情况
      instance_1({
        method: "get",
        url: "/common/log/getDataTotal",
        params: this.queryForm,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.getLan(res.data.data);
        }
      });

      this.listLoading = false;
    },

    getChart(value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        if (this.isColor == "0") {
          zheiTitle.push(item.key + "月");
        } else {
          zheiTitle.push(item.key + "日");
        }
        zheValue.push(item.value);
      });
      let TextBox = "代表点击率趋势统计";
      let option = {
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        tooltip: {
          trigger: "axis",
        },
        // legend: {
        //   itemHeight: 15
        // },

        grid: {
          left: "8%",
          right: "6%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          //x轴文字旋转
          // axisLabel: {
          //   rotate: 330,
          //   interval: 0
          // },
          type: "category",
          data: zheiTitle,
        },
        yAxis: {
          splitNumber: 10,
          type: "value",
        },
        series: [
          {
            name: "点击次数",
            data: zheValue,
            type: "line",
          },
        ],
      };
      var myChart = echarts.init(document.getElementById("data"));
      myChart.setOption(option);
    },
    getDeng(value) {
      let zheiTitle = [];
      let zheValue = [];

      value.forEach((item) => {
        if (this.isColor == "0") {
          zheiTitle.push(item.key + "月");
        } else {
          zheiTitle.push(item.key + "日");
        }

        zheValue.push(item.value);
      });
      let TextBox = "代表登陆情况";

      let option = {
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        tooltip: {
          trigger: "axis",
        },
        // legend: {
        //   itemHeight: 15
        // },

        grid: {
          left: "8%",
          right: "6%",
          bottom: "0%",
          containLabel: true,
        },
        xAxis: {
          //x轴文字旋转
          // axisLabel: {
          //   rotate: 330,
          //   interval: 0
          // },
          type: "category",
          data: zheiTitle,
        },
        yAxis: {
          splitNumber: 10,
          type: "value",
        },
        series: [
          {
            name: "登陆人次",
            data: zheValue,
            type: "line",
          },
        ],
      };
      var myChart = echarts.init(document.getElementById("landing"));
      myChart.setOption(option);
    },

    getShi(value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.label);
        zheValue.unshift({
          name: item.label,
          value: item.value,
        });
      });
      const worker = echarts.init(document.getElementById("worker"));
      let TextBox = "工作人员使用情况";
      worker.setOption({
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        legend: {
          orient: "vertical",
          data: zheiTitle,
          x: "left",
          right: 0,
          top: 40,
        },
        tooltip: {
          trigger: "item",
          formatter: "{b} : {c} ({d}%)",
        },
        series: [
          {
            type: "pie",
            data: zheValue,
            label: { show: false },
            radius: ["40%", "70%"],
            center: ["60%", "50%"],

            itemStyle: {
              // 柱状图的背景色
              normal: {
                barBorderRadius: [0, 80, 80, 0],
                color: function (params) {
                  // 给出颜色组
                  var colorList = [
                    "#2b82e1",
                    "#7a48e7",
                    "#ac33c0",
                    "#01a008",
                    "#02baae",
                    "#04baae",
                    "#a5d740",
                    "#d53132",
                    "#ffe507",
                    "#ff8d1b",
                  ];
                  //调用
                  return colorList[params.dataIndex];
                },
              },
            },
          },
        ],
      });
      worker.on("click", (params) => {
        this.$router.push({
          path: "/iChartData",
          query: { queryForm: this.queryForm, moudleName: params.name },
        });
      });
    },
    getDai(value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.label);
        zheValue.unshift(item.value);
      });
      document.getElementById("behalf").setAttribute("_echarts_instance_", "");
      this.behalf = echarts.init(document.getElementById("behalf"));
      let TextBox = "代表访问各栏目情况统计";
      this.behalf.setOption(
        {
          title: {
            text: TextBox,
            padding: [2, 0, 0, 14],
          },
          yAxis: {
            type: "category",
            data: zheiTitle,
            axisTick: {
              // 刻度
              show: false, // 不显示刻度线
            },
            axisLine: {
              // 设置轴线
              show: false,
            },
          },
          xAxis: {
            type: "value",
            splitLine: {
              // 去除背景网格线
              show: false,
            },
            axisTick: {
              // 刻度
              show: false, // 不显示刻度线
            },
            axisLine: {
              // 设置轴线
              show: false,
            },
            axisLabel: {
              show: false,
            },
          },
          grid: {
            left: "12%",
            right: "22%",
            bottom: "0%",
            containLabel: true,
          },
          series: [
            {
              data: zheValue,
              type: "bar",
              showBackground: true,
              barWidth: "50%",
              backgroundStyle: {
                color: "rgba(180, 180, 180, 0.2)",
              },
              label: {
                show: true,
                position: "right",
                formatter: function (params) {
                  // data  name
                  return params.data + "人次";
                },
              },
              itemStyle: {
                // 柱状图的背景色
                normal: {
                  barBorderRadius: [0, 80, 80, 0],
                  color: function (params) {
                    // 给出颜色组
                    var colorList = [
                      "#2b82e1",
                      "#1e1e1e",
                      "#ac33c0",
                      "#01a008",
                      "#04baae",
                      "#a5d740",
                      "#d53132",
                      "#ff8d1b",
                    ];
                    //调用
                    return colorList[params.dataIndex];
                  },
                },
              },
            },
          ],
        },
        true
      );
      this.behalf.on("click", (params) => {
        console.log(params);
        this.$router.push({
          path: "/iChartData",
          query: { queryForm: this.queryForm, moudleName: params.name },
        });
      });
    },
    getLan(value) {
      let zheiTitle = [];
      let zheValue = [];
      value.forEach((item) => {
        zheiTitle.unshift(item.label);
        zheValue.unshift(item.value);
      });
      const getColumn = echarts.init(document.getElementById("getColumn"));

      let TextBox = "各栏目新增数据情况统计";

      getColumn.setOption({
        title: {
          text: TextBox,
          padding: [2, 0, 0, 14],
        },
        yAxis: {
          type: "category",
          data: zheiTitle,

          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
        },
        xAxis: {
          type: "value",
          splitLine: {
            // 去除背景网格线
            show: false,
          },
          axisTick: {
            // 刻度
            show: false, // 不显示刻度线
          },
          axisLine: {
            // 设置轴线
            show: false,
          },
          axisLabel: {
            show: false,
          },
        },
        grid: {
          left: "12%",
          right: "20%",
          bottom: "0%",
          containLabel: true,
        },
        series: [
          {
            data: zheValue,
            type: "bar",
            showBackground: true,
            barWidth: "50%",
            backgroundStyle: {
              color: "rgba(180, 180, 180, 0.2)",
            },
            label: {
              show: true,
              position: "right",
              formatter: function (params) {
                let title = "";
                value.forEach((item) => {
                  if (item.label == params.name) {
                    title = params.data + item.unit;
                  }
                });
                return title;
              },
            },
            itemStyle: {
              // 柱状图的背景色
              normal: {
                barBorderRadius: [0, 80, 80, 0],
                color: function (params) {
                  // 给出颜色组
                  var colorList = [
                    "#2a6d7c",
                    "#2b82e1",
                    "#7a48e7",
                    "#ac33c0",
                    "#01a008",
                    "#02baae",
                    "#a5d740",
                    "#d53132",
                    "#ff8d1b",
                    "#4608ad",
                  ];
                  //调用
                  return colorList[params.dataIndex];
                },
              },
            },
          },
        ],
      });
      getColumn.on("click", (params) => {
        // this.$router.push({ path: "/iChartData", query: { queryForm: this.queryForm, moudleName: params.name } });
      });
    },
    checkPermission,
  },
};
</script>
<style>
/* IE 浏览器 */
.scrollbar {
  /*三角箭头的颜色*/
  scrollbar-arrow-color: rgb(71, 69, 69);
  /*滚动条滑块按钮的颜色*/
  scrollbar-face-color: #9999;
  /*滚动条整体颜色*/
  scrollbar-highlight-color: #9999;
  /*滚动条阴影*/
  scrollbar-shadow-color: #9999;
  /*滚动条轨道颜色*/
  scrollbar-track-color: #fff;
  /*滚动条3d亮色阴影边框的外观颜色——左边和上边的阴影色*/
  scrollbar-3dlight-color: #9999;
  /*滚动条3d暗色阴影边框的外观颜色——右边和下边的阴影色*/
  scrollbar-darkshadow-color: #9999;
  /*滚动条基准颜色*/
  scrollbar-base-color: #9999;
  border: 0px;
  border-radius: 10px !important;
}

.ehArts-box {
  height: 100%;
  width: 100%;
}

.ehArts {
  margin: 40px 0;
  height: 600px;
  width: 100%;
}
.ehArts-box .beizhu {
  padding: 0 0.4rem;
  margin: 0;
  text-align: left;
  margin-bottom: 1.8rem;
}
.ttbox {
  position: relative;
}
.ttbox .dw {
  position: absolute;
  top: -0.5rem;
  right: 0.5rem;
}
</style>
