<template>
  <div class="knowledge table-container">
    <a-spin :indicator="indicator" :spinning="listLoading">
      <div style="width:100%;margin-left:0">
        <a-form-model layout="horizontal" :model="queryForm">
          <div>
            <a-row>
              <!-- <a-col :md="5" :sm="24">
              <a-form-model-item label="查看年份" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }>
                <a-date-picker v-model="queryForm.createDate"
                allow-clear value-format="YYYY" mode="year" ref="years"
                  placeholder="选择时间"
                  :open="yearShow"
                   @openChange="openChangeOne($event)"
                 @panelChange="panleChangeOne($event)"
                  style="width: 100%;">
                </a-date-picker>
              </a-form-model-item>
            </a-col> -->
              <a-col :span="4">
                <a-form-model-item label="条件" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.queryType" placeholder="请选择条件" allow-clear show-search
                    @change="changeType()" style="width:100%">
                    <a-select-option v-for="item in queryTypeOptions" :key="item.value" :label="item.label"
                      :value="item.value">
                      {{ item.label }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="4"  v-if="queryForm.queryType == 'year'">
                <a-form-model-item label="年份" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.startTime" placeholder="请选择年份" allow-clear show-search
                   >
                    <a-select-option v-for="item in yearList" :key="item" :label="item" :value="item">
                      {{ item }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="4"  v-if="queryForm.queryType == 'month'">
                <a-form-model-item label="月份" :labelCol="{ span: 5 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-month-picker v-model="queryForm.startTime" style="width:100%" value-format="YYYY-MM" allow-clear />
                </a-form-model-item>
              </a-col>
              <a-col :md="4" :sm="24" v-if="queryForm.queryType == 'interval'">
                <a-form-item label="开始时间" prop="startTime" :labelCol="{ span: 6 }"
                  :wrapperCol="{ span: 18, offset: 0 }">
                  <a-date-picker v-model="queryForm.startTime" allow-clear value-format="YYYY-MM-DD "
                    placeholder="选择开始时间" style="width:100%" :disabled-date="
                      (current) =>
                        current && queryForm.endTime
                          ? current.valueOf() >=
                          moment(new Date(queryForm.endTime)).valueOf()
                          : false
                    "></a-date-picker>
                </a-form-item>
              </a-col>
              <a-col :md="4" :sm="24" v-if="queryForm.queryType == 'interval'">
                <a-form-item label="结束时间" prop="endTime" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-date-picker v-model="queryForm.endTime" allow-clear value-format="YYYY-MM-DD" placeholder="选择结束时间"
                    style="width:100%" :disabled-date="
                      (current) =>
                        current && queryForm.startTime
                          ? moment(new Date(queryForm.startTime)).valueOf() >=
                          current.valueOf()
                          : false
                    "></a-date-picker>
                </a-form-item>
              </a-col>
              <a-col :md="4" :sm="24">
                <a-form-model-item label="代表类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.dbType" placeholder="请选择代表类型" allow-clear show-search>
                    <a-select-option v-for="item in listDai" :key="item.value" :label="item.text" :value="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="4" :sm="24">
                <a-form-model-item label="数据类型" :labelCol="{ span: 6 }" :wrapperCol="{ span: 18, offset: 0 }">
                  <a-select v-model="queryForm.operType" placeholder="请选择数据类型" allow-clear show-search>
                    <a-select-option v-for="item in list1" :key="item.value" :label="item.text" :value="item.value">
                      {{ item.text }}
                    </a-select-option>
                  </a-select>
                </a-form-model-item>
              </a-col>
              <a-col :md="4" :sm="24"  style="float: right; ">
                <span style="float: right; margin-top: 3px;">
                  <a-button style="margin-top: 4px;" type="primary" @click="search">搜索</a-button>
                  <a-button style="margin-left: 12px;" @click="reset" class="pinkBoutton">重置</a-button>
                </span>

              </a-col>
            </a-row>

          </div>
        </a-form-model>
        <a-table :columns="columns" :rowKey="
          (record, index) => {
            return record.id + record.operCreateTime + index;
          }
        " :dataSource="dataSource" :pagination="pagination">
          <!-- <div slot="Filetitle" slot-scope="{ record }">
          <div v-html="record.title"  ></div>
        </div>  :customRow="clickRow"-->
        </a-table>
      </div>
    </a-spin>
  </div>
</template>

<script>
import { instance_1, instance_4 } from "@/api/axiosRq";
import { myPagination } from "@/mixins/pagination.js";
import Vue from "vue";
import store from "@/store";
import { log } from '@antv/g2plot/lib/utils';
import moment from "moment";
export default {
  mixins: [myPagination],
  data() {
    return {
      // loading样式
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      listLoading: false,
      yearShow: false,
      treeData: [],
      replaceFields: {
        title: "name",
        key: "id",
        children: "children",
      },
      dataSource: [
      ],
      list1: [// 右侧菜单 选项数组
        { text: '全部', value: '' },
        { text: '登录', value: 0 },
        { text: '新增', value: 1 },
        { text: '更新', value: 2 },
        { text: '删除', value: 3 },
        { text: '查询', value: 4 },
      ],
      listDai: [// 右侧菜单 选项数组
        { text: '全部', value: '' },
        { text: '全国代表', value: 1 },
        { text: '省代表', value: 2 },
        { text: '市代表', value: 3 },
      ],
      listYong: [// 右侧菜单 选项数组
        { text: '全部', value: '' },
        { text: '代表', value: 0 },
        { text: '工作人员', value: 1 },
      ],
      indexNum: 1,
      // 列表
      columns: [
        {
          fixed: 'left',
          title: "序号",
          key: "index",
          align: "center",
          width: 80,
          ellipsis: true,
          customRender: (text, record, index) =>
            `${(this.indexNum - 1) * this.indexNum + index + 1}`,
        },
        {
          title: "姓名",
          dataIndex: "operUserName",
          align: "center",
          width: 350,
          ellipsis: true,
          //  scopedSlots: { customRender: "Filetitle" },
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "模块",
          align: "center",
          width: 250,
          ellipsis: true,
          dataIndex: "operModul",
          customRender: (text, record, index) => {
            return text || '/'
          },
        },
        {
          title: "数据类型",
          dataIndex: "operType",
          align: "center",
          width: 350,
          ellipsis: true,
          customRender: (text, record, index) => {
            if (text == "0") {
              return "登录";
            } else if (text == "1") {
              return "新增";
            } else if (text == "2") {
              return "更新";
            } else if (text == "3") {
              return "删除";
            }
            else if (text == "4") {
              return "查询";
            }
            else if (text == "") {
              return "全部";
            }
            // return text||'/'
          }
        },
        {
          title: "记录时间",
          dataIndex: "operCreateTime",
          align: "center",
          width: 350,
          ellipsis: true,
          customRender: (text, record, index) => {
            return text.replace("T", " ").split("Z").join("").substr(0, 19)
          }
        },
      ],
      queryTypeOptions: [
        { label: "年份", value: "year" },
        { label: "月份", value: "month" },
        { label: "自定义", value: "interval" },
      ],
      queryForm: {
        queryType: '',
        startTime: "",
        endTime: '',
        current: '1',
        size: '10',
        pageNum: "1",
        pageSize: "10",
        operType: '',
        moudleName: '',
        dbType: '',
        ifFindUser: 0,
      },
      yearList: []
      // // 分页器设置
      // pagination: {
      //   pageNo: 1,
      //   pageSize: 10, // 默认每页显示数量
      //   showSizeChanger: true, // 显示可改变每页数量
      //   showQuickJumper: true, // 显示跳转功能
      //   pageSizeOptions: ["10", "20", "50", "100", "200", "500"],
      //   // 每页数量选项
      //   showTotal: (total) => `总共 ${total} 条`, // 显示总数
      //   onShowSizeChange: (current, pageSize) =>
      //     this.changePageSize(current, pageSize), // 改变每页数量时更新显示
      //   onChange: this.handleCurrentChange.bind(this), // 点击页码事件
      //   total: 0, // 总条数
      //   current: 0, // 当前页数
      //   buildOptionText: (pageSizeOptions) => `${pageSizeOptions.value}条/页`,
      //   size: "middle",
      // },
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "日志统计详情");
    var a = new Date();
    this.yearList[0] = a.getFullYear();
    for (var i = 0; i < 4; i++) {
      var num = this.yearList[this.yearList.length - 1] - 1
      this.yearList.push(num);
    }
    let { startTime, endTime, queryType , ifFindUser} = this.$route.query.queryForm
    this.queryForm.startTime = startTime
    this.queryForm.endTime = endTime
    this.queryForm.queryType = queryType
    this.queryForm.ifFindUser = ifFindUser
    // console.log(this.$route.query.moudleName);
    if (this.$route.query.moudleName) {
      this.queryForm.moudleName = this.$route.query.moudleName
    }
    this.fetchData()
  },
  methods: {
    changeType() {
      if (this.queryForm.queryType == 'year') {
        this.isColor = "0"
        this.queryForm.startTime = moment(new Date(new Date())).format('YYYY')
        this.queryForm.endTime = ''
      } else if (this.queryForm.queryType == 'month') {
        this.isColor = "1"
        this.queryForm.startTime = moment(new Date(new Date())).format('YYYY-MM')
        this.queryForm.endTime = ''
      } else {
        this.isColor = "1"
        this.queryForm.startTime = moment(new Date(new Date())).format('YYYY-MM-DD')
        this.queryForm.endTime = ''
      }

    },
    openChangeOne(e) {
      if (e) {
        this.yearShow = true;
      }
    },
    panleChangeOne(val) {
      console.log(val._d)
      // let time =val._d.$format("YYYY");
      var time = val._d;
      this.yearShow = false;
      this.queryForm.createDate = moment(time).format("YYYY");

    },
    // 时间转换
    formatDateTiem: function (date) {
      var y = date.getFullYear();
      var m = date.getMonth();
      m = m < 10 ? ('0' + m) : m;
      var d = date.getDate();
      d = d < 10 ? ('0' + d) : d;
      var h = date.getHours();
      var minute = date.getMinutes();
      minute = minute < 10 ? ('0' + minute) : minute;
      return y

    },

    // 重置
    reset() {
      this.queryForm = {
        queryType: this.queryForm.queryType,
        startTime: this.queryForm.startTime,
        endTime: '',
        current: '1',
        size: '10',
        pageNum: "1",
        pageSize: "10",
        operType: '',
        moudleName: this.queryForm.moudleName,
        dbType: '',
        ifFindUser: this.queryForm.ifFindUser,
      };
      this.pagination.current = 1;
      this.pagination.pageSize = 10;
      this.fetchData();
    },
    // dataTree () {
    //   instance_1({
    //     url: "/home/<USER>/informList",
    //     method: "get",
    //     params:{
    //     categoryId:"8479835eea2d421da0f2bac5d49e8ad0"
    //   }
    //   }).then((res) => {
    //     if (res.data.code == '0000') {
    //       this.treeData = res.data.data;
    //        this.treeData.forEach((item) => {
    //         delete item.root;
    //         if (item.children) {
    //           item.children.forEach((i, n) => {
    //             delete i.root;
    //           });
    //         }
    //       });
    //     }
    //   });
    // },
    // handleClick (e, data) {
    //   //
    //   // console.log(data.node.dataRef);
    //   // console.log(data.node.dataRef.id,"as");
    //   this.queryForm.categoryId = data.node.dataRef.id
    //   this.fetchData()
    // },
    fetchData() {
      this.queryForm.current = this.queryForm.pageNum;
      this.queryForm.size = this.queryForm.pageSize;
      console.log("this.queryForm",this.queryForm)
      this.listLoading = true;
      instance_1({
        url: "/common/log/findPage",
        method: "get",
        params: this.queryForm,
        headers: {
          flag: false
        }
      }).then((res) => {
        this.dataSource = res.data.data.records;
        this.pagination.total = res.data.data.total;

        this.listLoading = false;
      });
    },
    search() {
      this.queryForm.pageNum = 1
      this.queryForm.current = 1
      this.pagination.current = 1
      this.fetchData()
    },

    download(recode) {
      let { doc, id, title } = recode
      console.log(doc);
      // 将斜杆相反
      let doc_file = doc.replace(/\\/g, "/");
      console.log(doc_file);
      if (doc == "") { }
      else if (doc.indexOf('pdf') > -1) {
        // 原先 这里的tonken
        // let pdfUrl =
        //   Vue.prototype.GLOBAL.basePath_1 +"/file/view?file=" +doc +"&token=" +store.getters.accessToken;
        // console.log(pdfUrl);
        //  window.open(
        //   process.env.BASE_URL +
        //   "pdfjs/web/viewer.html?file=" +
        //   encodeURIComponent(pdfUrl)
        // );
        // +
        let pdfUrl = Vue.prototype.GLOBAL.basePath_1 +
          "/resource/viewPDF?file=" + doc_file + "&token=" + Vue.prototype.GLOBAL.token;
        console.log(pdfUrl);
        window.open(
          process.env.BASE_URL +
          "pdfjs/web/viewer.html?file=" +
          encodeURIComponent(pdfUrl)
        );

      } else {
        instance_1({
          url: "/repository/download",
          method: "get",
          responseType: "blob",
          params: { path: doc, articleId: id }
        }).then((res) => {
          let a = window.document.createElement("a");
          res = URL.createObjectURL(res.data);
          a.href = res;
          let type = ".doc"
          if (doc.indexOf('xls') > -1) {
            type = ".xls"
          } else {
            type = ".doc"
          }
          a.download = title + type;
          window.document.body.appendChild(a);
          a.click();
          window.document.body.removeChild(a);
        });
      }
    },
    // // 点击行
    // clickRow(record) {
    //   return {
    //     props: {},
    //     on: {
    //       // 事件
    //       click: (event) => {
    //         if ("__vue__" in event.target) {
    //           let content = event.target.attributes[0].textContent;
    //           instance_1({
    //             url: "/home/<USER>/findArticleContent",
    //             method: "get",
    //             params: { contentId: event.target.__vue__.record.id }
    //           }).then((res) => {
    //             this.$router.push({
    //               path: "/training/knowledgeData",
    //               query: {
    //                 data: res.data.data
    //               }
    //             })
    //           });
    //           // if (content.indexOf('-') > -1 && content.indexOf(':') > -1) { }
    //           // else {

    //           // }
    //         }
    //       },
    //     },
    //   };
    // },
  },
};
</script>
<style lang="scss" scoped>
.knowledge {
  width: 100%;
  height: 100%;
  padding: 20px;
  display: flex;

  .title {
    height: 40px;
    line-height: 40px;
    background-color: #fbeeef;
    border-radius: 6px;
    text-align: center;
    font-family: PingFang-M;
  }

  .knowledge-left {
    width: 18%;
    overflow-y: auto;
    height: 600px;
  }

  .knowledge-right {
    margin-left: 2%;
    width: 80%;
  }

  .condition {
    margin-left: 10px;
  }
}
</style>
