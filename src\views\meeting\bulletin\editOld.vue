<template>
  <div>
    <el-container>
      <el-main>
        <a-row :gutter="15">
          <a-col>

            <a-button
              v-if="!this.$route.query.id"
              style="margin-left: 35px;"
              @click="backText"
              >复制上一条数据</a-button
            >
          </a-col>
          <a-col
            :xs="24"
            :sm="{ span: 20, offset: 2 }"
            :md="{ span: 20, offset: 2 }"
            :lg="{ span: 14, offset: 5 }"
            :xl="{ span: 12, offset: 6 }"
          >
            <a-spin
              class="spin"
              :indicator="indicator"
              :spinning="listLoading"
            />

            <a-form-model
              ref="noticeForm"
              :model="noticeForm"
              :rules="rules"
              :label-col="{ span: 6 }"
              :wrapper-col="{ span: 16 }"
            >
              <a-form-model-item label="届次" prop="jcDm">
                <a-select v-model="noticeForm.jcDm" :disabled="diState">
                  <a-select-option
                    v-for="item in typeGradation"
                    :key="item.jcDm"
                    :value="item.jcDm"
                    >{{ item.jcmc }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
              <a-form-model-item label="标题" prop="title">
                <a-input
                  v-model="noticeForm.title"
                  :disabled="diState"
                  autocomplete="off"
                ></a-input>
              </a-form-model-item>
              <a-form-model-item label="周期" prop="num">
                第
                <a-input-number
                  v-model="noticeForm.num"
                  :disabled="diState"
                  :min="1"
                  autocomplete="off"
                ></a-input-number>
                期
              </a-form-model-item>
              <a-form-model-item label="发布单位" prop="orgName">
                <div class="searchStyle">
                  <a-input
                    v-model="noticeForm.orgName"
                    disabled
                    enter-button
                  ></a-input>
                  <a-button
                    :disabled="diState"
                    type="primary"
                    icon="search"
                    @click="handlePublish"
                  ></a-button>
                </div>
                <!-- <a-input-search v-model="noticeForm.orgName"
                                enter-button
                @search="handlePublish()"></a-input-search>-->
              </a-form-model-item>

              <a-form-model-item
                label="受邀范围"
                prop="resumptionWeekInviteeList"
              >
                <div class="searchStyle">
                  <a-input
                    v-model="noticeForm.resumptionWeekInviteeList"
                    disabled
                    enter-button
                  ></a-input>
                  <a-button
                    :disabled="diState"
                    type="primary"
                    icon="search"
                    @click="handleRel"
                  ></a-button>
                </div>
                <!-- <a-input-search enter-button
                                v-model="noticeForm.resumptionWeekInviteeList"
                @search="handleRel"></a-input-search>-->
              </a-form-model-item>
              <a-form-model-item label="周报内容">
                <editor
                  v-model="noticeForm.content"
                  :min-height="192"
                  :height="230"
                />
              </a-form-model-item>
              <!-- <a-form-model-item label="是否发送短信">
                <a-checkbox-group v-model="noticeForm.type" :disabled="diState">
                  <a-checkbox value="0">短信</a-checkbox>
                </a-checkbox-group>
              </a-form-model-item> -->
              <a-form-model-item label="周报附件" prop="fileUrl">
                <!-- <el-upload
                  class="upload-demo"
                  :action="action"
                  :file-list="fileUrl"
                  :on-remove="handleRemove"
                  :on-success="handle_success"
                  :on-error="handleError"
                  :http-request="uploadFile"
                  :on-change="handleChange"
                  :on-preview="handlePdfView"
                  accept=".pdf"
                >
                  <a-button size="small" type="primary">点击上传</a-button>
                  <div slot="tip" class="el-upload__tip">
                    只能上传pdf文件
                  </div>
                </el-upload>-->

                <!-- 上传 -->
                <a-upload
                  :action="action"
                  accept=".pdf, .ofd"
                  :data="fileData"
                  :remove="handleRemove"
                  :before-upload="beforeAvatarUpload"
                  :file-list="fileUrl"
                  @preview="previewData"
                  @change="uploadChange"
                >
                  <a-button :disabled="diState" type="primary"
                    >点击上传</a-button
                  >
                  <div>只能上传pdf,ofd文件，且不超过20mb</div>
                </a-upload>
              </a-form-model-item>
              <a-row style="text-align: center;">
                <a-button type="primary" :disabled="diState" @click="save(0)"
                  >草 稿</a-button
                >
                <a-button
                  :disabled="diState"
                  type="success"
                  style="margin-left: 12px;"
                  @click="save(1)"
                  >发 送</a-button
                >
              </a-row>
            </a-form-model>
          </a-col>
        </a-row>
      </el-main>
    </el-container>
    <identityRelJob ref="identityRelJob" :root-org-id="unit"></identityRelJob>
    <identity-rel ref="identityRel" :root-org-id="rootOrgId"></identity-rel>
    <orgTreeDialog
      ref="orgTreeDialog"
      :root-org-id="unit"
      @confirm="confirmOrg"
    ></orgTreeDialog>
  </div>
</template>

<script>
import moment from "moment";
import { instance_1, instance_2 } from "@/api/axiosRq";
import { addMessageInfo, getNoticeInfo, doSave, doUpdate } from "@/api/message";
import { getAllTypeList } from "@/api/identity";
import { doDelete as doFileDelete } from "@/api/file";
import orgTreeDialog from "@/views/common/orgTreeDialog.vue";
import { getById as getOrgById } from "@/api/organization";
import IdentityRel from "../../common/identityRel";
import checkPermission from "@/utils/permission";
import IdentityRelJob from "../../common/identityRelJob";
import { iShowNumber } from "@/utils/validate";
import store from "@/store";
import Vue from "vue";
import transferVue from "../../toRepresent/transfer.vue";

export default {
  name: "TableEdit",
  components: {
    IdentityRel,
    orgTreeDialog,
    IdentityRelJob,
  },
  data() {
    const validateNumber = (rule, value, callback) => {
      // 根据校验结果进行处理
      iShowNumber(value) ? callback() : callback(new Error("最大人数为正整数"));
    };
    return {
      plainOptions: ["不在机构内，请手工填写"],
      valuechecks: [],
      isvaluecheck: false,
      belongbelong: null,
      rootOrgId: "root",
      unit: "root",
      Neturl: "/activity/insert",
      disabled: false,
      beInvited: [],
      zhengchang: {
        start: "8:00",
        step: "00:15",
        end: "21:30",
      },
      zhengchang_1: {
        start: "08:00",
        step: "00:15",
        end: "21:30",
      },
      type: null,
      xuanze: 1,
      fabu: "",
      dialogImageUrl: "",
      dialogVisible: false,
      typeGradation: [],
      typeShang: [
        {
          idx: 0,
          value: "上午",
        },
        {
          idx: 1,
          value: "下午",
        },
      ],
      typeShang1: [
        {
          idx: 0,
          value: "上午",
        },
        {
          idx: 1,
          value: "下午",
        },
      ],
      action: process.env.VUE_APP_BASE_API + "/api/v1/activity/upload",
      fileData: {
        relId: "",
        type: 0,
        encrypted: true,
      },

      worker: 0, //工作人员类型
      workerOrganization: [], //工作人员组织
      workerTree: [], //工作人员树
      WorkerList: "", //工作人员现实拼接
      noticeId: "",
      relId: "",
      receiverTreeNodeList: [],
      receiverIdentityList: [],
      publishIdentityList: [],
      identityUserList: [],
      fromPage: null,
      defaultOrgId: null,
      header: null,
      relOrgName0: "",
      relOrgName1: "",
      fileSha: "",
      fileListImg: [],
      fd: new FormData(),
      fileUrl: [],
      listLoading: false,
      indicator: <a-icon type="loading" style="font-size: 24px" spin />,
      noticeForm: {
        num: "1",
        belong: "1",
        addType: 1,
        creator_org: null,
        jcDm: "",
        wordRange: "",
        shangType: "",
        xiaType: "",
        orgName: "",
        limitNumber: "",
        limit: "0",
        fenjie: [
          {
            date: "",
            content: "",
          },
        ],
        activityWorkerInfos: [],
        peizhi: [
          // {
          //   name: "乘车",
          //   content: "地点：广州市人大正门，时间：09:00",
          // },
        ],
        time_yy_start: "",
        time_yy_end: "",
        Time_start: "",
        Time_End: "",
        signUpStart: "",
        signUpEnd: "",
        signUpStart: "",
        signUpEnd: "",
        nature: "",
        fileName: "",

        id: "",
        isTop: "",
        address: "",
        isAnnouncement: "",
        title: "", //标题
        typeId: "", //类型id
        type: null, //类型
        source: "", //来源
        content: "",
        meetingId: "",
        imageUrl: "",
        publishId: "",
        publishName: "", //发布单位
        timingTime: "",
        time: [],
        sendTime: "", //定时发送的时间
        startTime: "",
        endTime: "",
        sendType: 1,
        sendTypeFlag: 1,
        status: null,
        diState: false,
        returnState: 1,
        returnS: 1,
        receiverList: [],
        //选择类型：1 身份选择 0 树选择
        receiverType: "0",
        fileList: [],
        carousel: 0,
        depict: "",
        resumptionWeekInviteeList: "",
        resumptionWeekInviteeListId: "",
        creatorId: "6",
        newresumptionWeekInviteeList: "",
      },
      fujian: [],
      imageUrl: null,
      title: "",
      menuTitle: "",
      listTitle: "",
      isNewData: true,
      pickerOptions: {
        shortcuts: [
          {
            text: "最近一周",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近一个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
              picker.$emit("pick", [start, end]);
            },
          },
          {
            text: "最近三个月",
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
              picker.$emit("pick", [start, end]);
            },
          },
        ],
      },

      rules: {
        limitNumber: [
          { required: true, message: "请输入最大报名人数", trigger: "blur" },
          {
            trigger: "blur",
            validator: validateNumber,
          },
        ],
        title: [{ required: true, message: "标题不能为空", trigger: "blur" }],
        address: [
          { required: true, message: "周报地点不能为空", trigger: "blur" },
        ],
        orgName: [
          { required: true, message: "组织单位不能为空", trigger: "blur" },
        ],
        // type: [
        //   { required: true, message: "周报类型不能为空", trigger: "change" },
        // ],
        startTime: [
          {
            required: true,
            message: "周报开始日期不能为空",
            trigger: "change",
          },
        ],
        endTime: [
          {
            required: true,
            message: "周报结束日期不能为空",
            trigger: "change",
          },
        ],
        signUpStart: [
          {
            required: true,
            message: "周报报名开始时间不能为空",
            trigger: "change",
          },
        ],
        signUpEnd: [
          {
            required: true,
            message: "周报报名结束时间不能为空",
            trigger: "change",
          },
        ],
        resumptionWeekInviteeList: [
          { required: true, message: "受邀范围不能为空", trigger: "change" },
        ],
        num: [{ required: true, message: "周期不能为空", trigger: "blur" }],
        content: [
          { required: true, message: "周报内容不能为空", trigger: "blur" },
        ],
      },
      typeOptions: [],
      sendTypeCheckBox: [],
      sendTypeCheckBox_1: [],
      defaultvalue: "",
    };
  },
  watch: {
    "noticeForm.notes": {
      handler: function () {
        if (this.noticeForm.notes.length == 100) {
          this.$message.error("最多只能输入100字");
        }
      },
    },
    "noticeForm.content": {
      handler: function () {
        if (this.noticeForm.content.length == 100) {
          this.$message.error("最多只能输入100字");
        }
      },
    },
    "noticeForm.endTime": {
      handler: function () {
        if (this.noticeForm.startTime != this.noticeForm.endTime) {
          this.typeShang1 = [
            {
              idx: 0,
              value: "上午",
            },
            {
              idx: 1,
              value: "下午",
            },
            {
              idx: 2,
              value: "晚上",
            },
          ];
        }
      },
    },
  },
  created() {
    // http://10.168.2.174:8080/protocol/listFullProtocolsPage
    this.initIdentityUserList();
    this.menuTitle = this.$route.matched[0].meta.title;
    this.listTitle = this.$route.query.listTitle;
    let { rootOrgId, qu, belongbelong, state } = this.$route.query;
    if (this.$route.query.id) {
      this.listLoading = true;
      this.acquireData();
    }

    this.diState = state;
    if (belongbelong == "2") {
      this.belongbelong = "2";
      // 判断是否区人大
      if (rootOrgId) {
        // 不等于null的话 一个接口 两个树形数据
        // this.noticeForm.rootOrgId=rootOrgId
        this.rootOrgId = rootOrgId;
        this.unit = qu;
        this.noticeForm.creator_org = qu;
      } else {
        //  null给默认值
        this.noticeForm.creator_org = "R_440100";
      }
    }

    // 是否为区
    let noticeType = this.$route.query.noticeType;

    this.typeOptions = [];
    this.typeNature = [];
    this.relId = this.$route.query.noticeId;
    this.fileData.relId = this.$route.query.noticeId;

    const noticeId = this.$route.query.noticeId;
    if (!noticeId) {
      this.title = "消息发送";
      this.isNewData = true;
      this.addMessageInfo();
    } else {
      this.title = "编辑消息";
      this.isNewData = false;
      this.initNoticeData(noticeId);
    }
    this.getByGradation();
    this.getactivityNature();
    this.getactivityType();

    this.$store.dispatch("navigation/breadcrumb1", "代表周报");
    this.$store.dispatch("navigation/breadcrumb2", "新增周报");
  },

  moreDate(date) {},
  methods: {
    //返回上一级
    previouspage() {
      this.$router.go(-1);
    },
    onChangeS(e) {
      console.log(e, "eeeeee");
      if (e.length == "1") {
        this.isvaluecheck = true;
      } else {
        this.isvaluecheck = false;
      }
    },
    disabledDate(current) {
      //给周报开始日期加1小时
      var date = new Date(this.noticeForm.startTime);
      var year = date.getFullYear();
      var month = date.getMonth() + 1;
      var day = date.getDate() + 1;
      var newTime = year + "-" + month + "-" + day;
      //当前时间
      var data = new Date();
      data.setHours(data.getHours() + 8);
      var endTime = data.toJSON().replace("T", " ").substring(0, 19);
      console.log(endTime, "endTimeendTime");
      //当前时间跟固定时分秒
      var datas = new Date();
      datas.setHours(datas.getHours() + 8);
      var DqTime = datas.toJSON().replace("T", " ").substring(0, 11);
      var RQdate = DqTime + "12" + ":" + "00" + ":" + "00";
      console.log(
        DqTime + "12" + ":" + "00" + ":" + "00" > endTime,
        "RQdateRQdatemeendTime"
      );
      //当前时间跟固定时分秒
      var Isdatas = new Date();
      Isdatas.setHours(Isdatas.getHours() + 8);
      var wSTime = Isdatas.toJSON().replace("T", " ").substring(0, 11);
      var wSdate = wSTime + "18" + ":" + "00" + ":" + "00";
      console.log(
        wSdate + "18" + ":" + "00" + ":" + "00" > endTime,
        "wSdatewSdateTimeendTime"
      );
      //noticeForm.startTime 周报开始日期
      //noticeForm.endTime 周报结束日期
      //noticeForm.signUpStart 报名开始时间
      //noticeForm.signUpEnd 报名结束时间
      //周报开始日期 noticeForm.shangType
      console.log(endTime < RQdate, "endTime < RQdate");
      if (this.noticeForm.shangType == "0") {
        this.defaultvalue = this.noticeForm.startTime;
        return current &&
          this.noticeForm.startTime &&
          this.noticeForm.signUpStart
          ? current.valueOf() >=
              moment(new Date(this.noticeForm.startTime)).valueOf() ||
              moment(new Date(this.noticeForm.signUpStart)).valueOf() >=
                current.valueOf()
          : false;
      } else if (this.noticeForm.shangType == "1") {
        if (endTime < RQdate) {
          return current &&
            this.noticeForm.startTime &&
            this.noticeForm.signUpStart
            ? current.valueOf() >= moment(newTime).valueOf() ||
                moment(new Date(this.noticeForm.signUpStart)).valueOf() >=
                  current.valueOf()
            : false;
        }
      } else {
        return current &&
          this.noticeForm.startTime &&
          this.noticeForm.signUpStart
          ? current.valueOf() >=
              moment(new Date(this.noticeForm.startTime)).valueOf() ||
              moment(new Date(this.noticeForm.signUpStart)).valueOf() >=
                current.valueOf()
          : false;
      }
      if (this.noticeForm.shangType == "2") {
        if (endTime < wSdate) {
          return current &&
            this.noticeForm.startTime &&
            this.noticeForm.signUpStart
            ? current.valueOf() >= moment(newTime).valueOf() ||
                moment(new Date(this.noticeForm.signUpStart)).valueOf() >=
                  current.valueOf()
            : false;
        }
      } else {
        return current &&
          this.noticeForm.startTime &&
          this.noticeForm.signUpStart
          ? current.valueOf() >=
              moment(new Date(this.noticeForm.startTime)).valueOf() ||
              moment(new Date(this.noticeForm.signUpStart)).valueOf() >=
                current.valueOf()
          : false;
      }
    },
    // 周报开始时间清空
    mergeDateTime1() {
      this.noticeForm.endTime = "";
      this.noticeForm.signUpStart = "";
      this.noticeForm.signUpEnd = "";
    },
    // 周报结束时间清空
    mergeDateTime2() {
      if (
        this.noticeForm.endTime < this.noticeForm.signUpStart ||
        this.noticeForm.endTime < this.noticeForm.signUpEnd
      ) {
        this.noticeForm.signUpStart = "";
        this.noticeForm.signUpEnd = "";
      }
      // if (this.noticeForm.startTime == this.noticeForm.endTime) {
      //   if (this.noticeForm.shangType == 1) {
      //     this.typeShang1 = [
      //       {
      //         idx: 1,
      //         value: "下午",
      //       },
      //       {
      //         idx: 2,
      //         value: "晚上",
      //       },
      //     ];
      //   } else if (this.noticeForm.shangType == 2) {
      //     this.typeShang1 = [
      //       {
      //         idx: 2,
      //         value: "晚上",
      //       },
      //     ];
      //   }
      // } else {
      //   this.typeShang1 = [
      //     {
      //       idx: 0,
      //       value: "上午",
      //     },
      //     {
      //       idx: 1,
      //       value: "下午",
      //     },
      //     {
      //       idx: 2,
      //       value: "晚上",
      //     },
      //   ];
      // }
    },

    backText() {
      this.listLoading = true;
      instance_1({
        method: "get",
        url: "/resumptionWeek/list",
        params: { pageNum: 1, pageSize: 10 },
      }).then((res) => {
        this.acquireData(res.data.rows[0].id, "type");
      });
    },
    // 清除
    handleRemove(file, fileList) {
      this.fujian.forEach((item, index) => {
        if (item.path == file.url) {
          this.fujian.splice(index, 1);
        }
      });
      this.fileUrl.forEach((item, index) => {
        if (item.url == file.url) {
          this.fileUrl.splice(index, 1);
        }
      });
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传
    uploadChange({ file, fileList }) {
      this.fileUrl = fileList;
      this.$set(this.noticeForm, "fileUrl", URL.createObjectURL(file));
      // 上传
      var wj = new FormData();
      wj.append("file", file);
      instance_2({
        method: "post",
        url: "/activity/upload",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        data: wj,
      })
        .then((res) => {
          this.fujian.push({
            newName: res.data.data[0].newName,
            fileName: res.data.data[0].originName,
            path: res.data.data[0].path,
            type: 2,
          });
        })
        .catch((error) => {});
    },
    // 上传大小限制
    beforeAvatarUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      // var reg = /^.+(\/pdf)$/;
      var reg = /(pdf|ofd)$/i;
      const isPDF = reg.test(file.type);
      if (!isPDF) {
        this.$message.error("上传的文件格式只能是PDF和OFD");
      }
      if (isLt20M && isPDF) return false;
    },
    currentSel(value) {
      const rule = this.typeOptions.find((item) => item.type === value);
      console.log(value);
      if (value == "参加市人民代表大会") {
        this.typeShang1 = [
          {
            idx: 0,
            value: "上午",
          },
          {
            idx: 1,
            value: "下午",
          },
          {
            idx: 2,
            value: "晚上",
          },
        ];
        this.typeShang = [
          {
            idx: 0,
            value: "上午",
          },
          {
            idx: 1,
            value: "下午",
          },
          {
            idx: 2,
            value: "晚上",
          },
        ];
      } else {
        this.typeShang1 = [
          {
            idx: 0,
            value: "上午",
          },
          {
            idx: 1,
            value: "下午",
          },
        ];
        this.typeShang = [
          {
            idx: 0,
            value: "上午",
          },
          {
            idx: 1,
            value: "下午",
          },
        ];
      }
      this.$set(this.noticeForm, "shangType", this.typeShang[0].idx);
      this.$set(this.noticeForm, "xiaType", this.typeShang1[1].idx);
      this.noticeForm.typeId = rule.id;
    },

    // 获取届次列表data
    getByGradation: function () {
      instance_2({
        method: "get",
        url: "/session/list",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          sfdm: 3,
        },
      })
        .then((data) => {
          this.typeGradation = data.data.data;
          this.noticeForm.jcDm = this.typeGradation[0].jcDm;
        })
        .catch((error) => {});
    },
    // 获取性質data
    getactivityNature: function () {
      instance_2({
        method: "get",
        url: "/activityNature/list",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {},
      })
        .then((data) => {
          console.log("🤗🤗🤗, data.data.data =>", data.data.data);
          this.typeNature = data.data.data;
        })
        .catch((error) => {});
    },
    // 获取类型
    getactivityType: function () {
      instance_2({
        method: "get",
        url: "/activityType/list",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {},
      })
        .then((data) => {
          this.typeOptions = data.data.data;
          //  this.noticeForm.shangType=this.typeOptions[0].idx;
          this.$set(this.noticeForm, "shangType", this.typeShang[0].idx);
          this.$set(this.noticeForm, "xiaType", this.typeShang1[1].idx);
        })
        .catch((error) => {});
    },
    selectChanged(value) {
      this.noticeForm.signUpEnd = "";
      if (this.noticeForm.startTime == this.noticeForm.endTime) {
        if (this.noticeForm.shangType == 1) {
          this.typeShang1[0] = "";
          this.noticeForm.xiaType = [1];
        } else if (this.noticeForm.shangType == 2) {
          this.typeShang1[0] = "";
          this.typeShang1[1] = "";
          this.noticeForm.xiaType = [2];
        }
      }
      if (value == 1) {
        this.zhengchang = {
          start: "12:00",
          step: "00:15",
          end: "21:30",
        };
      } else if (value == 0) {
        this.zhengchang = {
          start: "08:00",
          step: "00:15",
          end: "12:00",
        };
      }
    },
    peizhiAdd: function (status) {
      if (status == 1) {
        this.noticeForm.peizhi.push({
          value: "",
          text: "",
        });
      } else if (status == 2) {
        this.noticeForm.fenjie.push({
          date: "",
        });
      }
    },
    jiajiaAdd: function (status) {
      console.log(
        this.noticeForm.activityWorkerInfos,
        "this.noticeForm.activityWorkerInfos"
      );
      if (status == 1) {
        this.isvaluecheck = true;
        this.noticeForm.activityWorkerInfos.push({
          value: this.noticeForm.activityWorkerInfos.length,
          name: "",
        });
      } else if (status == 2) {
        this.noticeForm.fenjie.push({
          date: "",
        });
      }
    },
    // 报名截止时间为周报开始前一天（-）
    mergeDateTime() {
      //
      var nowDate = new Date(this.noticeForm.startTime);
      //减去1天
      var aimDate = new Date(nowDate.setDate(nowDate.getDate() - 1));

      this.noticeForm.signUpEnd = this.formatDate(aimDate);
    },
    formatDate(time) {
      var date = new Date(time);
      var year = date.getFullYear(),
        month = date.getMonth() + 1, //月份是从0开始的
        day = date.getDate(),
        hour = date.getHours(),
        min = date.getMinutes(),
        sec = date.getSeconds();
      var newTime =
        year +
        "-" +
        (month < 10 ? "0" + month : month) +
        "-" +
        (day < 10 ? "0" + day : day);
      // (hour < 10? '0' + hour : hour) + ':' +
      // (min < 10? '0' + min : min) + ':' +
      // (sec < 10? '0' + sec : sec);

      return newTime + " 23:59:59";
    },

    peizhiJian: function (status) {
      if (status == 1) {
        if (this.noticeForm.peizhi.length != 1) {
          this.noticeForm.peizhi.pop();
        }
      } else if (status == 2) {
        if (this.noticeForm.fenjie.length != 1) {
          this.noticeForm.fenjie.pop();
        }
      }
    },
    // jianjianJian: function (status) {
    //   if (status == 1) {
    //     if (this.noticeForm.activityWorkerInfos.length != 1) {
    //       this.noticeForm.activityWorkerInfos.pop();
    //     }
    //   } else if (status == 2) {
    //     if (this.noticeForm.fenjie.length != 1) {
    //       this.noticeForm.fenjie.pop();
    //     }
    //   }
    // },
    jianjianJian1: function (status) {
      console.log(status, "status");
      let activityWorkerInfos = this.noticeForm.activityWorkerInfos;
      let index = activityWorkerInfos.findIndex((el) => el.value == status);
      this.noticeForm.activityWorkerInfos.splice(index, 1);
    },
    // activityWorkerInfos
    handleOperation(command) {
      this.$message("click on item " + command);
    },
    // uploadFile(file) {
    //   var wj = new FormData();
    //   wj.append("file", file.file);

    //   instance_2({
    //     method: "post",
    //     url: "/activity/upload",
    //     headers: {
    //       "Content-type": "application/x-www-form-urlencoded",
    //       //  "Content-type": "multipart/form-data",
    //     },
    //     data: wj,
    //   })
    //     .then((data) => {
    //       this.fujian.push({
    //         newName: data.data.data[0].newName,
    //         fileName: data.data.data[0].originName,
    //         path: data.data.data[0].path,
    //         type: 2,
    //       });
    //       this.fileUrl.push({
    //         newName: data.data.data[0].newName,
    //         name: data.data.data[0].originName,
    //         url: data.data.data[0].path,
    //       });
    //     })
    //     .catch((error) => {});
    // },
    handle_success: function (file, fileList) {
      //  let fd = new FormData();

      this.noticeForm.fileName = fileList; //附件的名字
    },
    handleError: function (data) {},
    handleRemoveImg(file, fileList) {},
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogVisible = true;
    },
    handlePreviewImg(file) {},
    handleExceedImg(files, fileList) {
      this.$message.warning(
        `当前限制选择 3 个文件，本次选择了 ${files.length} 个文件，共选择了 ${
          files.length + fileList.length
        } 个文件`
      );
    },
    beforeRemoveImg(file, fileList) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    addMessageInfo() {
      addMessageInfo().then((res) => {
        this.noticeForm.id = res.data.id;
        this.relId = res.data.id;
        this.fileData.relId = res.data.id;
      });
    },
    initIdentityUserList() {
      getAllTypeList().then((res) => {
        this.identityUserList = res.data;
      });
    },
    //返回上一页
    goBackFront() {
      this.$store.dispatch("tagsView/delView", this.$route);
      // 根据来源页返回
      if (this.fromPage == "userManage") {
        this.$router
          .push({
            path: "/meetingManage/noticeList",
            params: {},
          })
          .catch(() => {});
      } else if (this.fromPage == "relManage") {
        this.$router
          .push({
            path: "/meetingManage/noticeList",
            params: {
              orgId: this.defaultOrgId,
              backPage: "user",
            },
          })
          .catch(() => {});
      } else {
        this.$router.go(-1);
      }
    },
    handleSuccess(response, file, fileList) {
      this.$baseMessage(`上传完成! `, "success");
    },

    // handleRemove(file, fileUrl) {
    //   const index = this.fujian.findIndex(
    //     (item) => item.newName == file.newName
    //   );
    //   this.fujian.splice(index, 1);
    //   const index1 = this.fileUrl.findIndex(
    //     (item) => item.newName == file.newName
    //   );
    //   this.fileUrl.splice(index1, 1);

    //   doFileDelete(file.id).then((res) => {});
    //   this.$baseMessage(`删除成功`, "success");
    // },
    // handleChange(file, fileList) {
    //   this.$set(this.noticeForm, "fileUrl", URL.createObjectURL(file.raw));
    // },

    //保存
    save(status) {
      // 去重
      this.wordbeInvited = this.oneFn(this.wordbeInvited);
      this.beInvited = this.oneFn(this.beInvited);
      this.$refs["noticeForm"].validate((valid) => {
        if (valid) {
          this.listLoading = true;
          this.disabled = true;
          let id = "";
          let urlData;
          if (this.noticeForm.type !== null) {
            this.noticeForm.type = this.noticeForm.type[0];
          }
          if (this.$route.query.id) {
            id = this.$route.query.id;
            urlData = "/resumptionWeek/update";
          } else {
            urlData = "/resumptionWeek/add";
          }
          instance_2({
            //  baseURL:'http://10.168.2.127:8080/api/v1',
            method: "post",
            url: urlData,
            headers: {
              "Content-type": "application/json",
            },
            data: {
              id: id,
              state: status, //判断是否为草稿
              resumptionWeekAttachments: this.fujian,
              title: this.noticeForm.title, //标题
              // type: this.noticeForm.type, //类型
              type: this.noticeForm.type || null, //类型
              num: this.noticeForm.num, //周期
              content: this.noticeForm.content, //内容
              rangeType: this.noticeForm.receiverType, //范围标识
              worker: this.worker, //工作人员范围标识
              activityWorkerList: this.wordbeInvited, //工作人员范围
              issueUnit: this.fabu, //组织单位id
              orgName: this.noticeForm.orgName, //组织单位名
              resumptionWeekInviteeList: this.beInvited, //受邀范围
              activityNeedList: this.noticeForm.peizhi, //周报需求
              activityWorkerInfos: this.noticeForm.activityWorkerInfos,
              jcDm: this.noticeForm.jcDm, //届次
            },
          })
            .then((data) => {
              if (data.data.code == "0000") {
                this.$message.success("请求成功");
                this.listLoading = false;
                this.$router.go(-1);
              } else {
                this.listLoading = false;
                this.$message.error(data.data.msg);
              }
            })
            .catch((error) => {});
        }
      });
    },
    oneFn(tempArr) {
      if (tempArr) {
        for (let i = 0; i < tempArr.length; i++) {
          for (let j = i + 1; j < tempArr.length; j++) {
            if (tempArr[i].userId == tempArr[j].userId) {
              tempArr.splice(j, 1);
              j--;
            }
          }
        }
        return tempArr;
      } else {
        return;
      }
    },
    checkboxChange_1(e) {
      let arr = this.sendTypeCheckBox_1;
      if (arr.length == "0") {
        this.noticeForm.sendType = "3";
      } else {
        arr.forEach((item) => {
          if (arr.length == "1") {
            if (item == "1") {
              this.noticeForm.sendType = "1";
            } else if (item == "2") {
              this.noticeForm.sendType = "2";
            }
          } else if (arr.length == "2") {
            this.noticeForm.sendType = "0";
          }
        });
      }
    },

    //工作人员选择器
    handleRelJob() {
      //
      // if (this.worker == "1") {
      //
      //   this.$refs["identityRelJob"].handleShow(
      //
      //     1,
      //     this.workerOrganization
      //   );
      // }
      if (this.worker == "0") {
        this.$refs["identityRelJob"].handleShow(0, this.workerTree);
      }
    },
    previewData(file) {
      let url = file.url || this.fujian[0].path;
      console.log(url);
      console.log(this.fujian.path);
      let pdfUrl =
        Vue.prototype.GLOBAL.basePath_1 +
        "/file/view?file=" +
        url +
        "&token=" +
        Vue.prototype.GLOBAL.token;

      window.open("/pdfjs/web/viewer.html?file=" + encodeURIComponent(pdfUrl));
    },
    // 机构选择器
    handlePublish() {
      //
      this.$refs.orgTreeDialog.open(this.publishIdentityList);
    },
    confirmOrg(checkedNodes) {
      //清空原有的发布单位
      this.publishIdentityList = [];
      this.noticeForm.orgName = "";
      this.fabu = "";
      if (checkedNodes.length > 0) {
        this.noticeForm.orgName = checkedNodes[0].orgName;
        this.fabu = checkedNodes[0].orgId;
        this.publishIdentityList.push(checkedNodes[0].orgId);
      }
      this.$refs["noticeForm"].validateField("orgName");
    },
    handleRel() {
      // if (this.noticeForm.receiverType == "1") {
      //   this.$refs["identityRel"].handleShow(
      //     this.noticeForm.receiverType,
      //     this.receiverIdentityList
      //   );
      // }
      this.$refs["identityRel"].handleShow(0, this.receiverTreeNodeList);
    },

    savePublish(checkedData) {
      //清空原有的发布单位
      this.publishIdentityList = [];
      this.noticeForm.publishName = "";
      this.noticeForm.publishId = "";
      let relName = "";
      if (checkedData.length > 0) {
        //遍历返回的身份id列表
        checkedData.forEach((identityId) => {
          //遍历身份人员列表
          this.identityUserList.forEach((identityUser) => {
            //找到对应身份的关联人员列表
            if (identityUser.id == identityId) {
              relName += identityUser.name + ",";
              this.noticeForm.publishId += identityUser.id + ",";
              this.publishIdentityList.push(identityUser.id);
              return false;
            }
          });
        });
        this.fabu = this.publishIdentityList;

        this.noticeForm.publishName = relName.substring(0, relName.length - 1);
        this.$refs["noticeForm"].validateField("publishName");
      }
    },

    saveRel(type, checkedData) {
      this.noticeForm.receiverType = type;

      //清空原有的接收范围
      this.receiverIdentityList = [];
      this.receiverTreeNodeList = [];
      this.noticeForm.receiverList = [];
      this.noticeForm.resumptionWeekInviteeList = "";
      let relName = "";
      let relId = "";
      this.beInvited = [];

      //身份选择赋值
      if (type == "1") {
        this.xuanze = type;
        if (checkedData.length > 0) {
          //遍历返回的身份id列表
          checkedData.forEach((identityId) => {
            //遍历身份人员列表
            this.identityUserList.forEach((identityUser) => {
              //找到对应身份的关联人员列表
              if (identityUser.id == identityId) {
                //降关联人员列表转换成接收人列表数据
                // identityUser.userList.forEach((identityUser) => {
                //   this.noticeForm.receiverList.push({
                //     receiverId: identityUser.userId,
                //     receiverName: identityUser.userName,
                //     relId: identityUser.identityId,
                //   });
                // });
                // let fanwei = [];

                relName += identityUser.name + ",";
                // this.receiverIdentityList.push(identityUser.id);
                this.receiverIdentityList.push(identityUser.id);
                //   this.restricted.push(identityUser.uniqueId);
                this.beInvited.push({
                  userName: identityUser.name,
                  inviteId: identityUser.uniqueId,
                });
                this.receiverTreeNodeList.push(identityUser.uniqueId);

                // fanwei.push({
                //   userName: identityUser.name,
                //    inviteId: identityUser.id,
                // });
                //
                return false;
              }
            });
          });

          this.noticeForm.receiverType = type;
          this.noticeForm.resumptionWeekInviteeList = relName.substring(
            0,
            relName.length - 1
          );
          this.$refs["noticeForm"].validateField("resumptionWeekInviteeList");
        }
      }

      //用户树选择赋值
      if (type == "0") {
        this.xuanze = type;
        if (checkedData.length > 0) {
          //遍历返回的身份id列表
          checkedData.forEach((treeNode) => {
            if (treeNode.itemType == "2") {
              // this.noticeForm.receiverList.push({
              //   receiverId: treeNode.id,
              //   receiverName: treeNode.name,
              //   relId: treeNode.uniqueId,
              // });

              relName += treeNode.name + ",";
              // 选择树结构时
              //   this.receiverTreeNodeList.push(treeNode.uniqueId);
              this.beInvited.push({
                type: type,
                userName: treeNode.name,
                inviteId: treeNode.uniqueId,
                userId: treeNode.id,
              });
              this.receiverTreeNodeList.push(treeNode.uniqueId); //
              //   this.receiverTreeNodeList.push(identityUser.uniqueId);
            }
          });

          this.noticeForm.receiverType = type;
          this.noticeForm.resumptionWeekInviteeList = relName.substring(
            0,
            relName.length - 1
          );
          this.noticeForm.sendScopeId = this.receiverTreeNodeList.join(",");
        }
      }
    },
    toRul() {
      this.$router.back(-1);
    },

    checkboxChange(dd) {
      let arr = this.type;
      if (arr.length == "0") {
        this.noticeForm.type = "3";
      } else {
        arr.forEach((item) => {
          if (arr.length == "1") {
            if (item == "1") {
              this.noticeForm.type = "1";
            } else if (item == "2") {
              this.noticeForm.type = "2";
            }
          } else if (arr.length == "2") {
            this.noticeForm.type = "0";
          }
        });
      }
    },

    saveRelJob(type, checkedData) {
      this.worker = type; //工作人员类型
      this.workerOrganization = []; //清空工作人员组织
      this.workerTree = []; //清空工作人员树
      let relName = "";
      let relId = "";
      this.wordbeInvited = [];

      //身份选择赋值
      if (type == "1") {
        this.xuanze = type;
        if (checkedData.length > 0) {
          //遍历返回的身份id列表
          checkedData.forEach((identityId) => {
            //遍历身份人员列表
            this.identityUserList.forEach((identityUser) => {
              //找到对应身份的关联人员列表
              if (identityUser.id == identityId) {
                //降关联人员列表转换成接收人列表数据
                // identityUser.userList.forEach((identityUser) => {
                //   this.noticeForm.receiverList.push({
                //     receiverId: identityUser.userId,
                //     receiverName: identityUser.userName,
                //     relId: identityUser.identityId,
                //   });
                // });
                // let fanwei = [];

                relName += identityUser.name + ",";
                // this.receiverIdentityList.push(identityUser.id);
                this.workerOrganization.push(identityUser.id);
                // this.wordTissue.push(identityUser.uniqueId);//存组织id
                this.wordbeInvited.push({
                  type: type,
                  userName: identityUser.name,
                  inviteId: identityUser.uniqueId,
                  userId: identityUser.id,
                });
                return false;
              }
            });
          });

          // this.noticeForm.wordRange = relName.substring(0, relName.length - 1);
          // this.$refs["noticeForm1"].validateField("wordRange");
        }
      }

      //用户树选择赋值
      if (type == "0") {
        this.xuanze = type;
        if (checkedData.length > 0) {
          //遍历返回的身份id列表
          checkedData.forEach((treeNode) => {
            if (treeNode.itemType == "2") {
              // this.noticeForm.receiverList.push({
              //   receiverId: treeNode.id,
              //   receiverName: treeNode.name,
              //   relId: treeNode.uniqueId,
              // });

              relName += treeNode.name + ",";
              // 选择树结构时
              //   this.receiverTreeNodeList.push(treeNode.uniqueId);
              this.wordbeInvited.push({
                type: type,
                userName: treeNode.name,
                inviteId: treeNode.uniqueId,
                userId: treeNode.id,
              });
              this.workerTree.push(treeNode.uniqueId);
              //   this.receiverTreeNodeList.push(identityUser.uniqueId);
            }
          });

          // this.noticeForm.wordRange = relName.substring(0, relName.length - 1);
          //   this.noticeForm.sendScopeId = this.receiverTreeNodeList.join(",");
        }
      }
    },
    // 获取详情
    acquireData: function (id, type) {
      instance_2({
        method: "get",
        url: "/resumptionWeek/getByid",
        headers: {
          "Content-type": "application/x-www-form-urlencoded",
        },
        params: {
          id: id || this.$route.query.id,
        },
      }).then((data) => {
        this.noticeForm = data.data.data;
        if (type) {
          this.noticeForm.type = null;
        }
        // this.Time_start = this.readJsonDate(this.noticeForm.startTime);
        // this.noticeForm.typeId = data.data.data.activityTypeId.toString();
        // this.Time_End = this.readJsonDate(this.noticeForm.endTime);
        // this.noticeForm.signUpEnd = this.noticeForm.signUpEnd;
        // this.noticeForm1.signUpEnd = this.noticeForm.signUpEnd;

        // this.noticeForm.signUpStart = this.noticeForm.signUpStart;
        // this.noticeForm1.signUpStart = this.noticeForm.signUpStart;
        // this.noticeForm.peizhi = data.data.data.activityNeedList;

        // this.noticeForm1.peizhi = this.noticeForm.peizhi;
        // this.noticeForm1.limit = data.data.data.confine.toString();
        // this.noticeForm1.sendTypeFlag = data.data.data.rangeType.toString();
        var checkedData = data.data.data.resumptionWeekInviteeList;
        this.beInvited = data.data.data.resumptionWeekInviteeList;
        // this.noticeForm1.receiverType = data.data.data.rangeType;
        this.receiverIdentityList = data.data.data.resumptionWeekInviteeList;
        this.receiverTreeNodeList = data.data.data.resumptionWeekInviteeList;
        var filePath = data.data.data.resumptionWeekAttachments;
        // this.publishIdentityList.push(this.noticeForm.orgId);
        var activityWorkerList = data.data.data.resumptionWeekInviteeList; //获得工作人员

        // this.noticeForm.shangType = data.data.data.startTimeRange;
        // this.noticeForm.xiaType = data.data.data.endTimeRange;
        // this.noticeForm.limitNumber=data.data.data.number;
        // this.$set(this.noticeForm, "limitNumber", data.data.data.number);
        if (filePath) {
          filePath.forEach((filePath) => {
            this.fileUrl.push({
              uid: "-1",
              newName: filePath.newName,
              name: filePath.fileName,
              url: filePath.path,
            });
            this.fujian.push({
              newName: filePath.newName,
              fileName: filePath.fileName,
              path: filePath.path,
              type: 2,
            });
          });
        }

        const rule = this.typeOptions.map(
          (item) => item.type === this.noticeForm.type
        );

        // ++

        //
        this.wordbeInvited = []; //清空，这个是update提交的值
        this.workerTree = []; //清空，这个是选择树的，需要传过去回显
        this.workerOrganization = []; //清空，这个是选择组织的
        let wordName = "";
        //
        // this.noticeForm.activityWorkerInfos = data.data.data.activityWorkerInfos;
        // this.noticeForm.activityWorkerInfos.forEach(el => {
        //   wordName += el.name + ",";
        //   this.wordbeInvited.push({
        //     userName: el.name,
        //   });

        // })
        //
        activityWorkerList.forEach((activityWorkerList) => {
          wordName += activityWorkerList.userName + ",";
          this.wordbeInvited.push({
            type: activityWorkerList.type,
            userName: activityWorkerList.userName,
            inviteId: activityWorkerList.inviteId,
            userId: activityWorkerList.userId,
          });
          this.worker = activityWorkerList.type;
          if (activityWorkerList.type == 1) {
            this.workerOrganization.push(activityWorkerList.inviteId);
          } else {
            this.workerTree.push(activityWorkerList.inviteId);
          }
        });
        // this.noticeForm1.wordRange = wordName.substring(0, wordName.length - 1); //回显拼接的数据
        this.receiverTreeNodeList = [];
        let relName = "";

        checkedData.forEach((checkedData) => {
          relName += checkedData.userName + ",";
          this.receiverTreeNodeList.push(checkedData.inviteId);
        });
        console.log(relName);
        this.$set(
          this.noticeForm,
          "resumptionWeekInviteeList",
          relName.substring(0, relName.length - 1)
        );
        this.listLoading = false;
      });
    },
    checkPermission,
  },
};
</script>

<style lang="scss" scoped>
.inputData {
  border-right: 0px;
  border-top-right-radius: 0px;
  border-bottom-right-radius: 0px;
}
.deleData {
  border-left: 0px;
  border-top-left-radius: 0px;
  border-bottom-left-radius: 0px;
}
.avatar-uploader .el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.avatar-uploader .el-upload:hover {
  border-color: #409eff;
}
.avatar-uploader-icon {
  // font-size: 28px;
  @include add-size($font_size_16);
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.avatar {
  width: 178px;
  height: 178px;
  display: block;
}
.flex_da,
.daxin {
  display: flex;
}
.daxin {
  flex-direction: column;
}
.daxin_i {
  display: flex;
  margin-top: 10px;
}
.flex_da > .el-date-editor {
  padding-left: 5px;
  flex: 4;
}
.flex_da > .a-select--small {
  flex: 2;
  padding-left: 5px;
}
.daxin_i > .a-input {
  margin-right: 10px;
  margin-top: 5px;
}
.a-form-model-item__content {
  display: flex;
}
.daxin {
  flex: 8;
}
.thr_main .btn {
  margin-top: 5px;
  height: 30px;
  padding: 0;
  flex: 1;
  // font-size: 18px;
  @include add-size($font_size_16);
}
.dddd {
  width: 120px;
  margin-left: 5px;
}
/* .a-form-model-item{
  display: flex;
  align-items: center;
}  */
.el-radio-group {
  height: 100%;
  padding-top: 5px;
}
.dianfei {
  display: flex;
}
.dianfei > .a-input {
  flex: 1;
  padding-right: 5px;
}
.el-upload-list__item-name {
  white-space: pre-wrap;
  text-overflow: inherit;
}
</style>

<style lang="scss" scoped>
.startTime {
  ::v-deep {
    .a-input__inner {
      width: 200px;
    }
  }
}

.endTime {
  ::v-deep {
    .a-input__inner {
      width: 200px;
    }
  }
  .el-popper {
    // left: 1278px !important;
  }
}
.xiaType {
  ::v-deep {
    .a-input__inner {
      //  width: 130px;
    }
  }
}
.tjgzry {
  position: relative;
}
.tjgzry1 {
  position: absolute;
  top: -6px;
  right: -218px;
}
.addWork {
  width: 36px;
  border-radius: 6px !important;
}
.spin {
  position: absolute;
  z-index: 9999999;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  -webkit-transform: translate(-50%, -50%);
  -moz-transform: translate(-50%, -50%);
  display: flex;
}
</style>
