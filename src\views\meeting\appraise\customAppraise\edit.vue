<template>
    <div style="padding: 20px">
      <a-form-model
        ref="form"
        :model="form"
        :rules="rules"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-model-item label="时间"
                               prop="serverTime">
          <a-date-picker style="width: 100%;"
                          value-format="YYYY年MM月DD日"
                          v-model="form.achieveDate"
                          placeholder="选择日期时间">
          </a-date-picker>
        </a-form-model-item>
        <a-form-model-item label="积分类型" prop="customAppraiseDm">
          <a-select v-model="form.customAppraiseDm"
                      placeholder="请选择积分类型"
                      allow-clear
                      show-search
                      :dropdownMatchSelectWidth="false"
                      @change="changeCustomAppraiseDm">
              <a-select-option v-for="item in customAppraiseList"
                              :key="item.customAppraiseDm"
                              :label="item.customAppraiseName"
                              :value="item.customAppraiseDm">
              {{ item.customAppraiseName }}
              </a-select-option>
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="区代表大会类型" prop="dhDm" v-if="form.customAppraiseDm == 5">
        <a-select     
                      v-model="form.dhDm"
                      show-search
                      option-filter-prop="children"
                      allow-clear
                      @change="handleChangeDhDm"
                      placeholder="请选择代表大会">
              <a-select-option v-for="item in conventionValue"
                               :key="item.dhDm"
                               :value="item.dhDm">{{ item.dhmc }}</a-select-option>
            </a-select>
        </a-form-model-item>

        <a-form-model-item label="届别" prop="jcDm" v-if="form.customAppraiseDm == 5">
          <a-select
            v-model="form.jcDm"
            placeholder="请选择届次"
            style="width: 100%;"
          >
            <a-select-option
              v-for="item in periodList"
              :key="item.jcDm"
              :value="item.jcDm"
              >{{ item.levelName }}
            </a-select-option>
          </a-select>
        </a-form-model-item>

        <a-form-model-item label="次" prop="levelNum" v-if="form.customAppraiseDm == 5">
          <a-input-number :min="1" :max="20" v-model="form.levelNum" :default-value="1" style="width: 100%;"/>
        </a-form-model-item>
        <a-form-model-item label="标题" prop="title">
          <a-input
            v-model="form.title"
          />
        </a-form-model-item>
        <a-form-model-item label="内容" prop="content">
          <a-input
            v-model="form.content"
          />
        </a-form-model-item>
        <a-form-model-item :wrapper-col="{ span: 18, offset: 3}">
          <a-button type="primary" @click="onSubmit"> 修  改 </a-button>
        </a-form-model-item>
      </a-form-model>
    </div>
  </template>
  
  <script>
  import { 
    getCustomAppraiseById,
    updateCustomAppraise,
    getDmCustomAppraise
  } from "@/api/appraise/appraise.js";
  import {
  conventionData,
} from "@/api/area.js";
  import {
    getLevelListByDhDm,
  } from "@/api/registrationMage/tableIng.js";
  export default {
    data() {
      return {
        recordId: "",
        //行政区域列表
        administrativeAreas: [],
        streetTowns: [],
        loading: false,
        imageUrl: "",
        labelCol: { span: 3 },
        wrapperCol: { span: 14 },
        other: "",
        form: {
          achieveDate: null
        },
        periodList: [],
        inviteRangeDesc: "",
        defaultSelect: [], //同行代表
        showDisabled: false,
        customAppraiseList: [],
        rules: {
          achieveDate: [
            {
              required: true,
              message: "请选择日期",
              trigger: "blur",
            },
          ],
          title: [
            {
              required: true,
              message: "请输入标题",
              trigger: "blur",
            },
          ],
          customAppraiseDm: [
            {
              required: true,
              message: "请选择积分类型",
              trigger: "blur",
            },
          ],
          dhDm: [
            {
              required: true,
              message: "请选择大会类型",
              trigger: "blur",
            },
          ],
          jcDm: [
            {
              required: true,
              message: "请选择届别",
              trigger: "blur",
            },
          ],
          levelNum: [
            {
              required: true,
              message: "请选择对应届次",
              trigger: "blur",
            },
          ],
        },
      };
    },
    mounted() {
      let { id } = this.$route.query;
      this.recordId = id;
      this.getDmCustomAppraiseList()
      // 获取区层级的代表大会列表
      this.convention("4")
      this.getById()
    },
    methods: {
      getById() {
        getCustomAppraiseById({ id : this.recordId }).then(res => {
          this.form = res.data
          if(res.data.dhDm) {
            this.getLevelListData(res.data.dhDm)
          }
        })
      },
      onSubmit() {
        this.$refs.form.validate((valid) => {
          if (valid) {
            updateCustomAppraise(this.form).then(res => {
              if(res.code == '0000') {
                this.$message.success("修改成功");
              } else {
                this.$message.error("修改失败");
              }
              this.getById()
            })
          } else {
            this.$message.error("请完善资料");
            return false;
          }
        });
      },
      resetForm() {
        this.$refs.form.resetFields();
      },
      getDmCustomAppraiseList () {
        getDmCustomAppraise().then(res => {
          this.customAppraiseList = res.data
        })
      },
      changeCustomAppraiseDm(val) {
      },
      changeCustomAppraiseDm(val) {
      },
      async convention (sfDm) {
        let res = await conventionData({ sfDm: sfDm});
        if (res.code == "0000") {
          this.conventionValue = res.data;
        }
      },
      handleChangeDhDm(val) {
        this.getLevelListData(val)
      },
      getLevelListData(dhDm) {
        getLevelListByDhDm({ dhDm: dhDm }).then((res) => {
          if (res.data.code == "0000") {
            this.periodList = res.data.data;
          } else {
            this.$message.error(res.data.message);
          }
        });
      },
    },
  };
  </script>
  
  <style lang="scss" scoped>
  .avatar-uploader > .ant-upload {
    width: 128px;
    height: 128px;
  }
  .ant-upload-select-picture-card i {
    font-size: 32px;
    color: #999;
  }
  
  .ant-upload-select-picture-card .ant-upload-text {
    margin-top: 8px;
    color: #666;
  }

  .searchStyle {
  display: flex !important;
  .ant-input {
    border-right: 0px;
    border-top-right-radius: 0px;
    border-bottom-right-radius: 0px;
  }
  .ant-btn-primary {
    width: 66px;
    border-left: 0px;
    border-top-left-radius: 0px;
    border-bottom-left-radius: 0px;
  }
}
  </style>
  