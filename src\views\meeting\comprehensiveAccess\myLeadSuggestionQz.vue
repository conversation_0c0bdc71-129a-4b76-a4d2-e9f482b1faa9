<template>
  <a-card>
    <div :class="advanced ? 'search' : null">
      <a-form layout="horizontal" :form="queryForm">
        <div>
          <a-row>
<!--            <a-col :md="6" :sm="24">-->
<!--              <a-form-item-->
<!--                label="建议届次"-->
<!--                :label-col="{ span: 6 }"-->
<!--                :wrapper-col="{ span: 18, offset: 0 }"-->
<!--              >-->
<!--                <a-select-->
<!--                  v-model="queryForm.meeting"-->
<!--                  placeholder="请选择建议届次"-->
<!--                  allow-clear-->
<!--                >-->
<!--                  <a-select-option-->
<!--                    v-for="item in periods"-->
<!--                    :key="item.meetingId"-->
<!--                    :value="item.meetingId"-->
<!--                    >{{ item.meetingSname }}</a-select-option-->
<!--                  >-->
<!--                </a-select>-->
<!--              </a-form-item>-->
<!--            </a-col>-->

            <a-col :md="6" :sm="24">
              <a-form-item
                label="标题"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18, offset: 0 }"
              >
                <a-input
                  v-model="queryForm.proposalTitle"
                  allow-clear
                  placeholder="请输入标题"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item
                label="领衔代表"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18, offset: 0 }"
              >
                <a-input
                  v-model="queryForm.headPer"
                  allow-clear
                  placeholder="请输入领衔代表"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <a-form-item
                label="建议号"
                :label-col="{ span: 6 }"
                :wrapper-col="{ span: 18, offset: 0 }"
              >
                <a-input
                  v-model="queryForm.proposalNum"
                  allow-clear
                  placeholder="请输入建议号"
                />
              </a-form-item>
            </a-col>
            <a-col :md="6" :sm="24">
              <span style="float: right; margin-top: 3px">
                <a style="margin-right: 8px" @click="toggleAdvanced">
                  {{ advanced ? "收起" : "高级搜索" }}
                  <a-icon :type="advanced ? 'up' : 'down'" />
                </a>
                <a-button type="primary" icon="upload" @click="showImportModal">批量导入</a-button>
                <a-button
                 style="margin-left: 12px"
                  type="primary"
                  @click="
                    () => {
                      queryForm.page = 1;
                      fetchData();
                    }
                  "
                  >搜索</a-button
                >
                <a-button
                  style="margin-left: 12px"
                  class="pinkBoutton"
                  @click="resetForm"
                  >重置</a-button
                >
              </span>
            </a-col>
          </a-row>
          <div v-if="advanced">
            <a-row>
              <a-col :md="6" :sm="24">
                <a-form-item
                  label="内容分类"
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 18, offset: 0 }"
                >
                  <a-select
                    v-model="queryForm.contentType"
                    placeholder="请选择内容分类"
                    allow-clear
                  >
                    <a-select-option
                      v-for="item in contentType"
                      :key="item.id"
                      :value="item.id"
                      >{{ item.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
<!--              <a-col :md="6" :sm="24">-->
<!--                <a-form-item-->
<!--                  label="办理情况"-->
<!--                  :label-col="{ span: 6 }"-->
<!--                  :wrapper-col="{ span: 18, offset: 0 }"-->
<!--                >-->
<!--                  <a-select-->
<!--                    v-model="queryForm.proposalHandle"-->
<!--                    placeholder="请选择办理情况"-->
<!--                    allow-clear-->
<!--                  >-->
<!--                    <a-select-option-->
<!--                      v-for="item in status"-->
<!--                      :key="item.id"-->
<!--                      :value="item.id"-->
<!--                      >{{ item.name }}</a-select-option-->
<!--                    >-->
<!--                  </a-select>-->
<!--                </a-form-item>-->
<!--              </a-col>-->
              <a-col :md="6" :sm="24">
                <a-form-item
                  label="议案类型"
                  :label-col="{ span: 6 }"
                  :wrapper-col="{ span: 18, offset: 0 }"
                >
                  <a-select
                    v-model="queryForm.proposalType"
                    placeholder="请选择议案类型"
                    allow-clear
                  >
                    <a-select-option
                      v-for="item in types"
                      :key="item.id"
                      :value="item.id"
                    >{{ item.name }}</a-select-option
                    >
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
        </div>
      </a-form>
    </div>
    <div>
<!--      <a-space class="operator">-->
<!--        <a-button type="primary" @click="exportExcel">导出Excel</a-button>-->
<!--        &lt;!&ndash; <a-button type="primary" >导出建议纸</a-button>-->
<!--        &ndash;&gt;-->
<!--      </a-space>-->
      <standard-table
        :columns="columns"
        :data-source="dataSource"
        :selected-rows.sync="selectedRows"
        :loading="TBloading"
        :pagination="pagination"
        row-key="proposalId"
        @clear="onClear"
        @change="onChange"
        @selectedRowChange="onSelectChange"
      >
        <div slot="show-title" slot-scope="{ text, record }">
          <a @click="showDesc(record)">{{ text }}</a>
        </div>
        <div slot="action" slot-scope="{ text, record }">
          <a-button
            type="link"
            @click="delData(record)"
            >删除</a-button
          >
        </div>
      </standard-table>
    </div>
    <!-- 新增/编辑 建议 对话框-->
    <a-modal
      :title="dialogTitle"
      :visible="dialogVisible"
      destroy-on-close
      :width="800"
      zIndex="98"
      centered
      @cancel="handleCancel"
    >
      <a-form-model
        ref="newModal"
        :model="formData"
        :label-col="{ span: 5 }"
        :wrapper-col="{ span: 14 }"
        :rules="rules"
      >
        <a-descriptions bordered class="mb2">
          <a-descriptions-item label="领衔代表">{{
            formData.headPer
          }}</a-descriptions-item>
          <a-descriptions-item label="议案建议类型"
            >闭会建议</a-descriptions-item
          >
          <a-descriptions-item label="手机号码">{{
            formData.mobile
          }}</a-descriptions-item>
          <a-descriptions-item
            label="详细通信地址"
            :span="2"
          ></a-descriptions-item>
          <a-descriptions-item label="邮编"></a-descriptions-item>
        </a-descriptions>
        <a-form-model-item label="标题" prop="proposalTitle">
          <a-input v-model="formData.proposalTitle" />
        </a-form-model-item>
        <a-form-model-item label="建议来源" prop="reason">
          <a-radio-group v-model="formData.reason">
            <a-radio value="1">专题调研</a-radio>
            <a-radio value="2">视察</a-radio>
            <a-radio value="3">座谈、走访等其他调研形式</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="是否需要书面答复" prop="codeHaveOrNo">
          <a-radio-group v-model="formData.codeHaveOrNo">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="意向主办单位">
          <a-input-search
            v-model="formData.intentOrg"
            enter-button
            @search="openUnitTable"
          >
          </a-input-search>
        </a-form-model-item>
        <a-form-model-item v-show="formData.proposalType == 1" label="内容分类">
          <a-select
            v-model="formData.proposalContentType"
            placeholder="请选择"
            allow-clear
          >
            <a-select-option
              v-for="item in contentType"
              :key="item.id"
              :value="item.id"
              >{{ item.name }}</a-select-option
            >
          </a-select>
        </a-form-model-item>
        <a-form-model-item label="联名代表">
          <a-input-search
            v-model="formData.operIdsName"
            enter-button
            @search="openJoinTable('联名')"
          >
          </a-input-search>
        </a-form-model-item>
        <a-form-model-item label="是否公开" prop="ifPublice">
          <a-radio-group v-model="formData.ifPublice">
            <a-radio value="1">是</a-radio>
            <a-radio value="2">否</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item
          label="多年提出、尚未解决事项"
          prop="overTheYearsNotResolved"
        >
          <a-radio-group v-model="formData.overTheYearsNotResolved">
            <a-radio value="1">是</a-radio>
            <a-radio value="0">否</a-radio>
            <a-radio value="2">未详</a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="加强与代表联系沟通" prop="ifContect">
          <a-radio-group v-model="formData.ifContect">
            <a-radio value="1"> 需要见面座谈和调研 </a-radio>
            <a-radio value="0"> 只需电话微信沟通 </a-radio>
            <a-radio value="2"> 不需要沟通，直接答复 </a-radio>
            <a-radio value="3"> 只做工作参考用，不需要正式书面答复 </a-radio>
          </a-radio-group>
        </a-form-model-item>
        <a-form-model-item label="建议正文内容" prop="contentId">
          <!-- 上传 -->
          <a-upload
            v-model="formData.contentId"
            action=""
            accept=".docx,.doc"
            :remove="handleTextRemove"
            :before-upload="beforeUpload"
            :file-list="fileTextList"
            @change="uploadChange($event, '9')"
          >
            <a-button :disabled="fileTextList.length == 1" type="primary"
              >点击上传</a-button
            >
            <div :hidden="fileTextList.length == 1">
              只能上传doc,docx文件，且不超过20mb
            </div>
          </a-upload>
        </a-form-model-item>
        <a-form-model-item label="正文以外附件">
          <a-upload
            action=""
            accept=".docx,.doc,.zip"
            :remove="handleAppendixRemove"
            :before-upload="
              () => {
                return false;
              }
            "
            :file-list="fileAppendixList"
            @change="uploadChange($event, '1')"
          >
            <a-button type="primary">点击上传</a-button>
          </a-upload>
        </a-form-model-item>
        <p>温馨提示：</p>
        <p>
          1.出于对附议代表的权利尊重，联名申请之后，若联名代表已进行反馈，则建议不可修改，选举单位、交办机关能查看，但无权处理。
        </p>
        <p>2.正式提交进入区人大校核状态，代表不可再进行修改建议。</p>
      </a-form-model>
      <div slot="footer">
        <a-button type="" @click="handleCancel">取消</a-button>
        <a-button type="primary" @click="handleOk">正式提交</a-button>
      </div>
    </a-modal>
    <!-- 新增建议详情弹窗 -->
    <a-modal
      :visible="detailModalVisible"
      :title="'建议详情'"
      width="750px"
      :footer="null"
      :bodyStyle="{ padding: '8px 16px 16px' }"
      @cancel="closeDetailModal"
    >
      <a-spin :spinning="detailLoading">
        <div class="detail-modal-container">
          <!-- 顶部信息区 -->
          <div class="detail-header">
            <div class="detail-title">{{ detailData.proposalTitle || '/' }}</div>
            <div class="detail-number">建议号：{{ detailData.proposalNum || '/' }}</div>
            <div class="detail-number">届次：{{ detailData.meeting || '/' }}</div>

          </div>


          <!-- 基本信息 -->
          <a-divider orientation="left" style="margin: 8px 0">
            <span class="divider-title">基本信息</span>
          </a-divider>
          <a-descriptions bordered :column="2" size="small" :colon="false" class="detail-descriptions">
            <a-descriptions-item label="领衔代表" :span="1">
              <span class="detail-value">{{ detailData.headPer || '/' }}</span>
            </a-descriptions-item>
            <!-- <a-descriptions-item label="代表手机" :span="1">
              <span class="detail-value">{{ detailData.mobile || '/' }}</span>
            </a-descriptions-item> -->
            <a-descriptions-item label="建议类型" :span="1">
              <span class="detail-value">{{ getProposalTypeText(detailData.proposalType) }}</span>
            </a-descriptions-item>
            <a-descriptions-item label="内容分类" :span="1">
              <span class="detail-value">{{ getContentTypeText(detailData.proposalContentType) }}</span>
            </a-descriptions-item>
            <!-- <a-descriptions-item label="意向主办单位" :span="1">
              <span class="detail-value">{{ detailData.intentOrg || '/' }}</span>
            </a-descriptions-item> -->
            <a-descriptions-item label="创建时间" :span="1">
              <span class="detail-value">{{ detailData.createTime || '/' }}</span>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 办理信息 -->
          <a-divider orientation="left" style="margin: 8px 0">
            <span class="divider-title">办理信息</span>
          </a-divider>
          <a-descriptions bordered :column="3" size="small" :colon="false" class="detail-descriptions">
            <a-descriptions-item label="办理状态" :span="1">
              <a-tag :color="getStatusColor(detailData.status)">{{ getStatusText(detailData.status) }}</a-tag>
            </a-descriptions-item>
            <!-- <a-descriptions-item label="建议来源" :span="2">
              <span class="detail-value">{{ detailData.reason == '1' ? '专题调研' : detailData.reason == '2' ? '视察' : detailData.reason == '3' ? '座谈、走访等其他调研形式' : '/' }}</span>
            </a-descriptions-item> -->
          </a-descriptions>

          <!-- 其他信息 -->
          <a-divider orientation="left" style="margin: 8px 0">
            <span class="divider-title">其他信息</span>
          </a-divider>
          <a-descriptions bordered :column="2" size="small" :colon="false" class="detail-descriptions">
            <a-descriptions-item label="是否需要书面答复" :span="1">
              <a-badge :status="detailData.codeHaveOrNo == '1' ? 'success' : 'default'" :text="detailData.codeHaveOrNo == '1' ? '是' :  '否' " />
            </a-descriptions-item>
            <a-descriptions-item label="是否公开" :span="1">
              <a-badge :status="detailData.ifPublice == '1' ? 'success' : 'default'" :text="detailData.ifPublice == '1' ? '是' : detailData.ifPublice == '2' ? '否' : '/'" />
            </a-descriptions-item>
            <a-descriptions-item label="多年提出、尚未解决事项" :span="2">
              <a-badge :status="detailData.overTheYearsNotResolved == '1' ? 'warning' : 'default'" :text="detailData.overTheYearsNotResolved == '1' ? '是' : detailData.overTheYearsNotResolved == '0' ? '否' : detailData.overTheYearsNotResolved == '2' ? '未详' : '/'" />
            </a-descriptions-item>
            <a-descriptions-item label="加强与代表联系沟通" :span="2">
              <span class="detail-value">{{ detailData.ifContect == '1' ? '需要见面座谈和调研' :
                 detailData.ifContect == '0' ? '只需电话微信沟通' :
                 detailData.ifContect == '2' ? '不需要沟通，直接答复' :
                 detailData.ifContect == '3' ? '只做工作参考用，不需要正式书面答复' : '/' }}</span>
            </a-descriptions-item>
          </a-descriptions>

          <!-- 按钮区域 -->
          <div class="detail-footer">
            <a-button size="small" @click="closeDetailModal">关闭</a-button>
          </div>
        </div>
      </a-spin>
    </a-modal>

     <!-- 批量导入弹窗 -->
    <a-modal
      title="批量导入建议"
      :visible="importModalVisible"
      @cancel="handleImportModalCancel"
      :footer="null"
      :maskClosable="false"
      :destroyOnClose="true"
      width="550px"
      class="import-modal"
    >
      <div class="import-modal-content">
        <div class="import-steps">
          <div class="step-item">
            <div class="step-number">1</div>
            <div class="step-info">
              <div class="step-title">下载导入模板</div>
              <div class="step-desc">请先下载Excel模板，按照模板格式填写数据</div>
              <a-button type="primary" icon="download" @click="downloadTemplate">下载模板</a-button>
            </div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item">
            <div class="step-number">2</div>
            <div class="step-info">
              <div class="step-title">上传填写好的Excel文件</div>
              <div class="step-desc">支持.xlsx格式，文件大小不超过10MB</div>
              <a-upload
                name="file"
                :multiple="false"
                :showUploadList="true"
                :beforeUpload="beforeImportUpload"
                :customRequest="() => {}"
                :fileList="importFileList"
                @change="handleImportChange"
                accept=".xlsx"
              >
                <a-button>
                  <a-icon type="upload" /> 选择文件
                </a-button>
              </a-upload>
              <div v-if="importFileList.length > 0" class="file-selected">
                <a-icon type="file-excel" theme="twoTone" twoToneColor="#52c41a" />
                <span class="file-name">已选择: {{ importFileList[0].name }}</span>
              </div>
            </div>
          </div>
          <div class="step-divider"></div>
          <div class="step-item">
            <div class="step-number">3</div>
            <div class="step-info">
              <div class="step-title">导入数据</div>
              <div class="step-desc">点击"确定导入"按钮，开始导入数据</div>
              <div class="import-tips">
                <a-icon type="info-circle" theme="twoTone" />
                <span>导入过程中请勿关闭窗口，导入完成后会自动刷新列表</span>
              </div>
            </div>
          </div>
        </div>

        <div class="import-footer">
          <a-button @click="handleImportModalCancel">取消</a-button>
          <a-button
            type="primary"
            @click="handleImportSubmit"
            :disabled="!importFile"
            :loading="importLoading">
            确定导入
          </a-button>
        </div>
      </div>
    </a-modal>
  </a-card>
</template>

<script>
import moment from "moment";
import StandardTable from "@/components/table/StandardTable";
import {
  getFindPageQz,
  proposalQzById,
  getJointlyList,
  suggestionUpData,
  findBySession,
  deleteByIdQz,
  getImportTemplate,
  importSuggest
} from "@/api/myJob/myProposalQz.js";
import { myPagination } from "@/mixins/pagination.js";
import commonSuggestInfo from "@/views/common/commonSuggestInfo.vue";
import commonUserShuttleTable from "@/views/common/commonUserShuttleTable.vue";
import commonUnitTable from "@/views/common/commonUnitTable.vue";
import { fileUpload, getAttachmentList } from "@/api/commonApi/file.js";
import { SJJSC } from "@/utils/enum/levelRoleMainIdentifyEnum";
import { getMyDbdhList, getMyDbdhListForLayer } from "@/api/system/dbdh";
import { data } from "jquery";
const columns = [
  {
    title: "建议号",
    width: 120,
    dataIndex: "proposalNum",
  },
  {
    title: "状态",
    ellipsis: true,
    width: 120,
    dataIndex: "status",
    // 10为草稿,11联名中,20校核中,30审核中,40,交办中,50为签收中,60答复中，70待反馈,80已反馈，90为办毕
    customRender: (text, record, index) => {
      if (text === 10) {
        return "草稿";
      }
      if (text === 11) {
        return "联名中";
      }
      if (text === 20) {
        return "校核中";
      }
      if (text === 21) {
        return "分类中";
      }
      if (text === 22) {
        return "初审中";
      }
      if (text === 30 || text === 25 ||text === 35) {
        return "复审中";
      }
      if (text === 40) {
        return "提出分办意见中";
      }
      if (text === 41) {
        return "不予立案审核中";
      }
      if (text === 42) {
        return "分办审核中";
      }
      if (text === 43) {
        return "分办复核中";
      }
      if (text === 45) {
        return "不予立案确认中";
      }
      if (text === 50) {
        return "签收中";
      }
      if (text === 60) {
        return "答复中";
      }
      if (text === 70) {
        return "待反馈";
      }
      if (text === 80) {
        return "已反馈";
      }
      if (text === 90) {
        return "办结";
      }
    },
  },
  {
    title: "类型",
    ellipsis: true,
    width: 120,
    dataIndex: "proposalType",
    customRender: (text, record, index) => {
      return text == 1
        ? "大会议案"
        : text == 2
        ? "大会建议"
        : text == 3
        ? "闭会建议"
        : text == 4
        ? "供参考建议"
        : "";
    },
  },
  {
    title: "标题",
    width: 200,
    dataIndex: "proposalTitle",
    scopedSlots: { customRender: "show-title" },
  },
  {
    title: "领衔代表",
    ellipsis: true,
    width: 120,
    dataIndex: "headPer",
  },
  // {
  //   title: "主办单位",
  //   ellipsis: true,
  //   width: 120,
  //   dataIndex: "zBDWName",
  // },
  // {
  //   title: "会办单位",
  //   ellipsis: true,
  //   width: 120,
  //   dataIndex: "hBDWName",
  // },

  {
    title: "代表公开意愿",
    dataIndex: "ifPublice",
    width: 150,
    ellipsis: true,
    customRender: (text, record, index) => {
      return text == 0 ? "否" : text == 1 ? "是" : text == 2 ? "已公开" : "";
    },
  },
  {
    title: "内容分类",
    ellipsis: true,
    width: 120,
    dataIndex: "proposalContentType",
    customRender: (text, record, index) => {
      let contentType = [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ];
      for (let index = 0; index < contentType.length; index++) {
        const element = contentType[index];
        if (element.id == text) {
          return element.name;
        }
      }
    },
  },
  // {
  //   title: "办理情况",
  //   ellipsis: true,
  //   width: 120,
  //   dataIndex: "proposalHandle",
  // },
  {
    title: "书面答复",
    ellipsis: true,
    width: 120,
    dataIndex: "codeHaveOrNo",
    customRender: (text, record, index) => {
      return text == 1 ? "是" : "否";

    },
  },
  // {
  //   title: "满意程度",
  //   ellipsis: true,
  //   width: 120,
  //   dataIndex: "evaluationString",
  //   customRender: (text, record, index) => {
  //     return text || "/";
  //     // return text == 1
  //     //   ? "不满意"
  //     //   : text == 2
  //     //   ? "一般"
  //     //   : text == 3
  //     //   ? "满意"
  //     //   : "";
  //   },
  // },

  {
    title: "创建时间",
    width: 120,
    ellipsis: true,
    dataIndex: "createTime",
  },
  {
    width: 200,
    align: "center",
    fixed: "right",
    title: "操作",
    scopedSlots: { customRender: "action" },
  },
];

// 大会建议列表
export default {
  name: "MotionMettingList",
  components: {
    StandardTable,
    commonSuggestInfo,
    commonUserShuttleTable,
    commonUnitTable,
  },
  // 引入分页器配置
  mixins: [myPagination],
  data() {
    return {
      levelRoleMainIdentify : SJJSC,
       // 批量导入相关
      importModalVisible: false,
      importFile: null,
      importFileList: [],
      importLoading: false,
      TBloading: false,
      advanced: false, // 高级搜索展开标记，默认为 false 不展开
      visible: false, // 建议详情弹出框显示标记
      columns: columns,
      dataSource: [],
      selectedRows: [],
      detailModalVisible: false, // 新增的详情弹窗控制
      detailLoading: false, // 详情加载状态
      detailData: {}, // 详情数据
      status: [
        // 办理情况
        { id: 1, name: "A（已解决或基本解决的问题）" },
        { id: 2, name: "B（列入年度计划解决的问题）" },
        { id: 3, name: "C（列入规划逐步解决的问题）" },
        { id: 4, name: "D（因条件限制或其他原因无法解决及作为参考的）" },
        { id: 5, name: "E（协办答复）" },
      ],
      types: [
        // 议案类型
        { id: 1, name: "大会议案" },
        { id: 2, name: "大会建议" },
        { id: 3, name: "闭会建议" },
        { id: 4, name: "供参考建议" },
      ],

      contentType: [
        // 内容分类
        { id: "JYFL01", name: "法制" },
        { id: "JYFL02", name: "监察和司法" },
        { id: "JYFL09", name: "预算" },
        { id: "JYFL03", name: "经济" },
        { id: "JYFL04", name: "城建环资" },
        { id: "JYFL05", name: "农村农业" },
        { id: "JYFL06", name: "教科文卫" },
        { id: "JYFL07", name: "华侨外事民族宗教" },
        { id: "JYFL10", name: "社会建设" },
        { id: "JYFL08", name: "其他" },
      ],

      deputationSelectedRows: [],
      queryForm: { page: 1, rows: 10, order: "desc" },
      formData: {
        headPer: "",
        operIdsName: "",
        operIds: "",
        intentOrg: "",
        codeHaveOrNo: "1",
        ifPublice: "1",
        overTheYearsNotResolved: "2",
        ifContect: "",
        fuJianIds: [],
        contentId: "",
      },
      dialogTitle: "新增", // 对话框标题
      dialogVisible: false,
      fileTextList: [], //文件正文文件列表
      fileAppendixList: [], //附件文件
      rules: {
        headPer: [
          { required: true, message: "请选择领衔代表", trigger: "blur" },
        ],
        proposalType: [
          { required: true, message: "请选择议案建议类型", trigger: "change" },
        ],
        proposalTitle: [
          { required: true, message: "请输入标题", trigger: "blur" },
        ],
        reason: [
          { required: true, message: "请选择建议来源", trigger: "change" },
        ],
        codeHaveOrNo: [
          {
            required: true,
            message: "请选择是否需要书面答复",
            trigger: "change",
          },
        ],
        ifPublice: [
          {
            required: true,
            message: "请选择是否公开",
            trigger: "change",
          },
        ],
        overTheYearsNotResolved: [
          {
            required: true,
            message: "请选择属于尚未解决事项",
            trigger: "change",
          },
        ],
        ifContect: [
          {
            required: true,
            message: "请选择加强与代表联系沟通",
            trigger: "change",
          },
        ],
        contentId: [
          {
            required: true,
            message: "请选择上传建议正文内容",
            trigger: "blur",
          },
        ],
      },
      defaultUnitRowKeys: [],
      defaultSelectUserKey: [],
    };
  },

  created() {
    this.$store.dispatch("navigation/breadcrumb1", "议案建议列表");
    this.$store.dispatch("navigation/breadcrumb2", "我领衔的建议");
    this.fetchData();
    this.getDbdhListForLayer();
    this.getPeriods();
  },
  methods: {
     showImportModal() {
      this.importModalVisible = true;
      this.importFile = null;
      this.importFileList = [];
    },
    handleEnter() {
      this.searchDebounced();
    },
    search () {
      this.queryForm.page = 1;
      this.fetchData();
    },
    moment,
    // 重置
    resetForm() {
      this.queryForm = Object.assign(
        {},
        this.$options.data.call(this).queryForm
      );
      this.fetchData();
    },
    // 获取届次
    getPeriods() {
      findBySession().then((res) => {
        if (res.data.code == 200) {
          this.periods = res.data.data.meetingMgr;
          this.periods.unshift({
            meetingId: "all",
            meetingSname: "全部",
          });
        }
      });
    },
    // 上传操作
    uploadChange(val, typeNum) {
      // 1、议案附件2、回复的附件3、代表大会议附件
      // 4、常委会议附件5、市政府(两院)宙议附件6
      // 承单位退回附件7、代表无需沟通证据8、座
      // 谈会证据9、建议正文10、建议纸
      if (val.file.status == "removed") return;
      let formData = new FormData();
      formData.append("file", val.file);
      formData.append("type", typeNum);
      fileUpload(formData).then((res) => {
        if (typeNum === "9") {
          // 建议正文
          this.formData.contentId = res.data.data[0].attId;
          this.fileTextList = val.fileList;
        } else {
          // 附件
          this.formData.fuJianIds.push(res.data.data[0].attId);
          val.fileList.forEach((item) => {
            if (item.uid == val.file.uid) {
              item.attId = res.data.data[0].attId;
            }
          });
          this.fileAppendixList = val.fileList;
        }
      });
    },
    // 获取附件列表
    getAttachmentListData() {
      getAttachmentList({
        proposalId: this.formData.proposalId,
        type: "",
      }).then((res) => {
        if (res.data.code == 200) {
          // 文件回显
          let file_zw = [];
          // 合并正文附件以及正文html的attId
          res.data.data.forEach((item) => {
            if (item.attType == 9 || item.attType == 11) {
              file_zw.push(item.attId);
            }
          });
          if (file_zw.length > 0) {
            console.log("🤗🤗🤗, file_zw =>", file_zw);
            // 获取正文附件
            let file_9 = res.data.data.filter((item) => item.attType == 9);
            file_9.map((item) => {
              item.attId = file_zw.toString();
              item.uid = item.attId;
              item.name = item.attName + item.attSuffix;
            });
            // 设置正文
            this.formData.contentId = file_9[0].attId;
            this.fileTextList = file_9;
          }
          let file_1 = res.data.data.filter((item) => item.attType == 1);
          if (file_1.length > 0) {
            file_1.map((item) => {
              item.uid = item.attId;
              item.name = item.attName + item.attSuffix;
            });
            this.fileAppendixList = file_1;
            // 设置附件
            this.formData.fuJianIds = file_1.map((item) => item.attId);
          }
          this.$forceUpdate();
        }
      });
    },
    // 取消
    handleCancel(e) {
      // 关闭表单对话框
      this.dialogVisible = false;
      this.formData = {
        headPer: "",
        operIdsName: "",
        operIds: "",
        intentOrg: "",
        codeHaveOrNo: "",
        ifPublice: "",
        overTheYearsNotResolved: "",
        ifContect: "",
        fuJianIds: [],
        contentId: "",
      };
      this.fileTextList = [];
      this.fileAppendixList = [];
    },
    // 编辑 保存
    handleOk(e) {
      // 设置文件入参
      this.formData.fuJianIds =
        this.formData.fuJianIds.toString() + "," + this.formData.contentId;
      // 设置状态值
      this.formData.status = 20;
      // 新增
      console.log("🤗🤗🤗, this.formData =>", this.formData);
      this.$refs["newModal"].validate((valid) => {
        if (valid) {
          // 编辑
          suggestionUpData(this.formData).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("编辑成功");
            } else {
              this.$message.error(res.data.message);
            }
            this.fetchData();
            this.handleCancel();
          });
        } else {
          this.$message.error("请补充完数据！");
        }
      });
    },
    // 打开联名代表table
    openJoinTable(type) {
      this.representType = type;
      if (type == "联名") {
        if (this.formData.operIds && this.formData.operIds.indexOf(",") >= 0) {
          // 设置已选的回显 多个情况
          this.defaultSelectUserKey = this.formData.operIds.split(",");
        } else {
          // 设置已选的回显 单个情况
          this.defaultSelectUserKey = [this.formData.operIds];
        }
        this.$refs.commonUserShuttleTable.multiple = true;
      } else {
        this.$refs.commonUserShuttleTable.multiple = false;
      }
      this.$refs.commonUserShuttleTable.jointTableVisible = true;
    },
    // 接受联名代表
    getJoinTable(rows) {
      if (this.representType == "联名") {
        this.formData.operIds = rows.map((item) => item.userId).toString();
        this.formData.operIdsName = rows.map((item) => item.name).toString();
      } else {
        // this.formData.nextOperation = rows
        //   .map((item) => item.userId)
        //   .toString();
        // this.formData.nextOperationName = rows
        //   .map((item) => item.name)
        //   .toString();
      }
    },
    // 正文文件删除
    handleTextRemove(file) {
      this.formData.contentId = "";
      this.fileTextList = [];
      this.$baseMessage(`删除成功`, "success");
    },
    // 上传之前
    beforeUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }

      var reg = /^.+(docx|doc)$/;
      const isDoc = reg.test(file.name.slice(file.name.lastIndexOf(".")));
      if (!isDoc) {
        this.$message.error("上传的文件格式只能是doc或docx");
        return;
      }
      return false;
    },
    // 附件文件删除
    handleAppendixRemove(file) {
      this.formData.fuJianIds = this.formData.fuJianIds.filter(
        (item) => item != file.attId
      );
      this.fileAppendixList = this.fileAppendixList.filter(
        (item) => item.attId != file.attId
      );
      this.$baseMessage(`删除成功`, "success");
    },
    // 接受意向主办单位
    getUnitTable(rows) {
      console.log("🤗接受意向主办单位🤗🤗, rows =>", rows);
      this.formData.intentOrg = rows[0].orgCode;
      this.formData.intentOrg = rows[0].orgName;
      this.defaultUnitRowKeys = [this.formData.intentOrg];
    },
    // 打开单位table
    openUnitTable() {
      this.$refs.commonUnitTable.getProposalUndertakesList();
    },
    // 单位/代表 回显
    echoData() {
      getJointlyList(this.formData.proposalId).then((res) => {
        if (res.data.code == 200) {
          let operIds = [];
          let operIdsName = [];
          for (const item of res.data.data) {
            if (item.ifpublic == 0) {
              operIds.push(item.operId);
            }
          }
          for (const item of res.data.data) {
            if (item.ifpublic == 0) {
              operIdsName.push(item.name);
            }
          }
          this.formData.operIds = operIds.toString();
          this.formData.operIdsName = operIdsName.toString();
        }
      });
      // getProposalUndertakes({
      //   orgName: "",
      // }).then((res) => {
      //   res.data.data.forEach((item) => {
      //     if (this.formData.intentOrg == item.orgCode) {
      //       this.formData.intentOrg = item.orgName;
      //     }
      //   });
      // });
    },
    // 删除
    delData(row) {
      this.$confirm({
        cancelText: "取消",
        okType: "danger",
        okText: "确定",
        title: "确定删除",
        content: "是否确定删除？",
        onOk: () => {
          deleteByIdQz(row.proposalId).then((res) => {
            if (res.data.code == 200) {
              this.$message.success("操作成功");
              this.fetchData();
            } else {
              this.$message.error(res.data.message);
            }
          });
        },
        onCancel: () => {
          this.$message.info("您已取消操作！");
        },
      });
    },
    // 编辑
    editData(row) {
      this.dialogTitle = "编辑建议";
      this.formType = "update"; // 当前为编辑状态
      proposalQzById({ proposalId: row.proposalId }).then((res) => {
        if (res.data.code == 200) {
          console.log("编辑建议, res.data.data =>", res.data.data);
          this.formData = Object.assign({}, this.formData, res.data.data);
          this.defaultUnitRowKeys = [this.formData.intentOrg];
          this.echoData();
          this.getAttachmentListData();
          this.dialogVisible = true;
        } else {
          this.$message.error(res.data.message);
        }
      });
    },
    // 获取数据
    fetchData() {
      getFindPageQz(this.queryForm).then((res) => {
        // this.TBloading = false;

        console.log("🤗🤗🤗, res =>", res);
        if (res.code === 200) {
          this.dataSource = res.rows;
          this.pagination.total = res.total;

        } else {
          this.$message.error(res.msg);
        }
      });
    },
    // 详情
    showDesc(record) {
      this.detailLoading = true;
      this.detailModalVisible = true;
      // 根据ID获取详情
      proposalQzById({ proposalId: record.proposalId }).then((res) => {
        console.log("🤗🤗🤗, res =>", res);
        if (res.code === "0000") {
          this.detailData = res.data;
        } else {
          this.$message.error(res.data.message);
        }
      }).finally(() => {
        this.detailLoading = false;
      });
    },

    getDbdhListForLayer () {
        if(this.levelRoleMainIdentify) {
          getMyDbdhListForLayer({levelRoleMainIdentify : this.levelRoleMainIdentify}).then((res) => {
            this.dbdhList = res.data;
            this.loadDefaultValue(0);
            this.defaultDhDmAndJcDm()

          });
        } else {
          getMyDbdhList({}).then((res) => {
            this.dbdhList = res.data;
            this.loadDefaultValue(0);
            this.defaultDhDmAndJcDm()

          });
        }

    },

    closeDetailModal() {
      this.detailModalVisible = false;
      this.detailData = {};
    },
    // 获取建议类型文字
    getProposalTypeText(type) {
      const typeMap = {
        '1': '大会议案',
        '2': '大会建议',
        '3': '闭会建议',
        '4': '供参考建议'
      };
      return typeMap[type] || '/';
    },
    // 获取内容分类文字
    getContentTypeText(type) {
      if (!type) return '/';
      for (let i = 0; i < this.contentType.length; i++) {
        if (this.contentType[i].id === type) {
          return this.contentType[i].name;
        }
      }
      return '/';
    },
    // 获取办理状态文字
    getStatusText(status) {
      if (status === 10) {
        return "待提交";
      }
      if (status === 20) {
        return "待审核";
      }
      if (status === 30) {
        return "审批通过";
      }
      if (status === 40) {
        return "立案中";
      }
      if (status === 50) {
        return "签收中";
      }
      if (status === 60) {
        return "答复中";
      }
      if (status === 70) {
        return "待反馈";
      }
      if (status === 80) {
        return "已反馈";
      }
      if (status === 90) {
        return "办结";
      }
      return '/';
    },
    // 获取状态对应的颜色
    getStatusColor(status) {
      const colorMap = {
        10: 'orange',     // 待提交
        20: 'purple',     // 待审核
        30: 'green',      // 审批通过
        40: 'blue',       // 立案中
        50: 'cyan',       // 签收中
        60: 'geekblue',   // 答复中
        70: 'volcano',    // 待反馈
        80: 'lime',       // 已反馈
        90: 'green'       // 办结
      };
      return colorMap[status] || 'default';
    },
    deleteRecord(key) {
      this.dataSource = this.dataSource.filter((item) => item.key !== key);
      this.selectedRows = this.selectedRows.filter((item) => item.key !== key);
    },
    toggleAdvanced() {
      this.advanced = !this.advanced;
    },
    remove() {
      this.dataSource = this.dataSource.filter(
        (item) =>
          this.selectedRows.findIndex((row) => row.key === item.key) === -1
      );
      this.selectedRows = [];
    },
    onClear() {},
    onStatusTitleClick() {},
    onChange() {},
    onSelectChange() {
      //
    },
    addNew() {
      // 添加记录
    },
    // 关闭导入弹窗
    handleImportModalCancel() {
      this.importModalVisible = false;
      this.importFile = null;
      this.importFileList = [];
    },
    // 文件上传前校验
    beforeImportUpload(file) {
      const isXLSX = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
      if (!isXLSX) {
        this.$message.error('只能上传 xlsx 格式的文件!');
        return false;
      }
      const isLt10M = file.size / 1024 / 1024 < 10;
      if (!isLt10M) {
        this.$message.error('文件大小不能超过 10MB!');
        return false;
      }

      // 直接在这里设置文件对象
      this.importFile = file;
      return false; // 手动处理上传
    },
    // 处理文件变化
    handleImportChange(info) {
      // 基于状态更新文件列表
      if (info.file.status === 'removed') {
        this.importFileList = [];
        this.importFile = null;
        return;
      }

      // 如果是文件选择事件，更新文件列表
      this.importFileList = [...info.fileList].slice(-1); // 只保留最后一个文件
      this.importFile = info.file;

      // 确认文件是否为空
      if (!this.importFile) {
        console.warn('警告：文件对象为空!');
      }
    },
    // 下载导入模板
    downloadTemplate() {
      this.$message.info('模板下载中，请稍候');
      // 这里需要替换为实际的模板下载接口
      // 假设接口为 getImportTemplate，类似于listPage.vue中的实现
      // 或者使用axios直接下载指定路径的模板文件

      // 示例代码，实际实现需要根据后端接口调整
      getImportTemplate().then((res) => {

      //   let a = window.document.createElement("a");
      //   let blob = new Blob([res.data], { type: 'application/vnd.ms-excel' });
      //   let url = window.URL.createObjectURL(blob);
      //   a.href = url;
      //   a.download = "建议批量导入模板.xlsx";
      //   window.document.body.appendChild(a);
      //   a.click();
      //   window.document.body.removeChild(a);
      // }).catch(err => {
      //   this.$message.error('模板下载失败');
      //   console.error('模板下载失败:', err);
          if (res.data && res.data instanceof Blob) {
      const url = window.URL.createObjectURL(res.data);
      const a = document.createElement('a');
      a.href = url;
      a.download = '建议批量导入模板.xlsx';
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      window.URL.revokeObjectURL(url);
    } else {
      // 打印 res.data 方便排查
      console.error('下载失败，返回内容：', res.data);
      this.$message.error('模板下载失败：返回内容格式错误');
    }
  })
  .catch((err) => {
    // 打印完整错误信息
    console.error('模板下载失败:', err);
    // 兼容 axios 的 err.response
    if (err && err.response && err.response.data) {
      this.$message.error('模板下载失败：' + (err.response.data.message || '接口异常'));
    } else {
      this.$message.error('模板下载失败：' + (err.message || '未知错误'));
    }
      });
    },
    // 提交导入文件
    handleImportSubmit() {
      if (!this.importFile) {
        this.$message.warning('请选择要导入的文件');
        return;
      }

      this.importLoading = true;

      // 构建表单数据
      const formData = new FormData();
      formData.append('file', this.importFile);
      formData.append('type', 'proposal'); // 根据实际情况设置文件类型

      // 调用导入接口 - 需要替换为实际的导入API
      importSuggest(formData).then(res => {
        this.importLoading = false;
        if (res.data && res.data.code === 200) {
          this.$message.success('导入成功');
          this.importModalVisible = false;
          this.fetchData(); // 刷新数据
        } else {
          this.$message.error(res.data.msg || '导入失败');
        }
      }).catch(error => {
        console.error('导入失败:', error);
        this.importLoading = false;
        this.$message.error('导入失败：' + (error.message || '未知错误'));
      });
    },
  },
};
</script>

<style lang="scss" scoped>
.search {
  margin-bottom: 10px;
}
.fold {
  width: calc(100% - 216px);
  display: inline-block;
}
.operator {
  margin-left: 10px;
  margin-bottom: 18px;
}
/* 建议详情弹窗样式 */  .detail-modal-container {
  padding: 4px;
  .detail-header {
    margin-bottom: 12px;
    border-bottom: 1px dashed #e8e8e8;
    padding-bottom: 8px;

    .detail-title {
      font-size: 16px;
      font-weight: 600;
      color: #222;
      margin-bottom: 6px;
      word-break: break-all;
    }

    .detail-number {
      font-size: 12px;
      color: #888;
    }
  }

  .divider-title {
    font-size: 13px;
    color: #1890ff;
    font-weight: 500;
  }

  .detail-descriptions {
    margin-bottom: 12px;

    :deep(.ant-descriptions-item-label) {
      background-color: #f5f7fa;
      width: 100px;
      font-weight: 500;
      padding: 8px 12px;
    }

    :deep(.ant-descriptions-item-content) {
      padding: 8px 12px;
    }

    .detail-value {
      word-break: break-all;
    }
  }

  .detail-footer {
    margin-top: 16px;
    text-align: center;
  }
}
.mt2 {
  margin-top: 36px;
}
@media screen and (max-width: 900px) {
  .fold {
    width: 100%;
  }
}
.ant-modal-mask, .ant-modal-wrap{
  z-index: 98;
}

/* 批量导入弹窗样式 */
.import-modal {
  ::v-deep .ant-modal-content {
    border-radius: 8px;
    overflow: hidden;
  }

  ::v-deep .ant-modal-header {
    background: #f0f5ff;
    border-bottom: 1px solid #d6e4ff;
  }

  ::v-deep .ant-modal-title {
    font-size: 16px;
    font-weight: bold;
    color: #1890ff;
  }

  ::v-deep .ant-modal-body {
    padding: 0;
  }
}

.import-modal-content {
  padding: 24px;
}

.import-steps {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.step-item {
  display: flex;
  gap: 16px;
}

.step-number {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background-color: #1890ff;
  color: white;
  border-radius: 50%;
  font-weight: bold;
}

.step-info {
  flex: 1;
}

.step-title {
  font-size: 16px;
  font-weight: 500;
  margin-bottom: 8px;
  color: rgba(0, 0, 0, 0.85);
}

.step-desc {
  color: rgba(0, 0, 0, 0.65);
  margin-bottom: 16px;
}

.step-divider {
  height: 24px;
  border-left: 2px dashed #d9d9d9;
  margin-left: 16px;
}

.import-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 24px;
  gap: 8px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.file-selected {
  margin-top: 8px;
  padding: 8px;
  background: #f6ffed;
  border: 1px solid #b7eb8f;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.file-name {
  margin-left: 8px;
  color: #52c41a;
  font-weight: 500;
}

.import-tips {
  margin-top: 10px;
  background: #e6f7ff;
  border: 1px solid #91d5ff;
  padding: 8px 12px;
  border-radius: 4px;
  display: flex;
  align-items: center;
}

.import-tips span {
  margin-left: 8px;
  color: #1890ff;
}
</style>
