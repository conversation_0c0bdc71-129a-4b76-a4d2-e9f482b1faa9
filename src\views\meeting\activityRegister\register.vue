<template>
  <div>
    <div class="TYcss" >
      <!-- 进社区 -->
      <div class="jbxx-contt" v-for="(large, largeIndex) in activityLarge" :key="largeIndex">
        <div class="jbqkss"><span class="jbqkbz">{{ large.name }}</span></div>
        <div style="padding-bottom: 20px;">
          <a-row>
            <!-- v-show="checkPermission(item.Power)"  -->
            <!-- <a-col v-for="(item, index) in large.items" :key="index" :span="3" -->
            <div v-for="(item, index) in large.items" :key="index" style="width:180px;float: left;margin-right: 20px"
              @click="skiUrl(item.activityTypeDm, item.url, item.activityClassify, item.title)">
              <div class="jbxxson Heightjbxxson cardHvover" :class="item.itemStyle == 1? 'disableItem' : ''">
                <img :src="item.icon" :onerror="defaultImg" :alt="item.title" />
                <!-- <img src="@/assets/images/StatisticalReport/履职登记管理-登记管理-登记活动/参加市人民代表大会.png" :alt="item.title" /> -->
                <div class="jbxxson_box" >
                  <a-popover title="" trigger="hover">
                    <template slot="content">
                      <div v-html="item.title"></div>
                    </template>
                    <div class="tits"  style="overflow: hidden;
                      -webkit-line-clamp: 2;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-box-orient: vertical;
                      " v-html="item.title"></div>
                  </a-popover>

                  <a-popover title="" trigger="hover">
                    <template slot="content">
                      <div v-html="item.subTitle"></div>
                    </template>
                    <div class="remarks"  style="overflow: hidden;
                      -webkit-line-clamp: 2;
                      text-overflow: ellipsis;
                      display: -webkit-box;
                      -webkit-box-orient: vertical;
                      " v-html="item.subTitle"></div>
                  </a-popover>
                  <!-- <div style="" class="remarks" v-html="item.subTitle"></div> -->
                </div>
              </div>
            </div>
          </a-row>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import "@/styles/StatisticalReport/StatisticalReport.scss";
import { getActivityTypeItemConfigList } from "@/api/registrationMage/tableIng.js"
import checkPermission from "@/utils/permission";
export default {
  name: "WorkIndex",
  components: {},
  data() {
    return {
      activityLarge: [],
      // tipValue: '【测试展示内容】表单标签告诉用户该表单需要填写什么类型的信息，也涉及到对用户输入内容进行校验与反馈，保证用户信息填写的完整度。单域由文本框、密码框、隐藏域、多行文本框、 复选框、单选框、下拉选择框和文件上传框等组成。',
    };
  },
  created() {
    this.$store.dispatch("navigation/breadcrumb1", "登记活动");
    this.$store.dispatch("navigation/breadcrumb2", "活动登记");
    this.initData()
  },
  computed: {
    defaultImg () {
      return 'this.src="' + require('@/assets/images/StatisticalReport/履职登记管理-登记管理-登记活动/参加市人民代表大会.png') + '"';
    }
  },
  methods: {
    checkPermission,
    initData() {
      getActivityTypeItemConfigList().then(res => {
        this.activityLarge = res.data.data
        console.log(this.activityLarge,'this.activityLarge');
      })
    },
    // @!@!@
    skiUrl(bizId, url, activityClassify, title) {
      console.log(url)
      if (url === '/delegate/addRegister') {
        console.log('跳转');
        this.$router.push({
          path: url,
          query: {
            bizId: bizId,
            code: bizId,
            textBox: activityClassify == 2 ? '活动' : '会议',
            title: title,
            tabIndex: 5,
          },
        });
      } else {
        console.log(bizId)
        if (bizId == "14") {
          console.log(this.$router.history.current.path)
          let locationSystem = {
            path: this.$router.history.current.path,
            systemCode: this.$router.history.current.meta.systemId,
          };
          sessionStorage.setItem(
            "locationSystem",
            JSON.stringify(locationSystem)
          );
          setTimeout(() => {
            window.top.postMessage(
              {
                event: "locationSystem", // 约定的消息事件
                args: {
                  path: url, //路径
                  systemCode: "lxrmqz", //子系统编码
                }, // 参数
              },
              "*"
            );
          });
        }

        if (bizId == "99") {
          console.log(this.$router.history.current.path)
          let locationSystem = {
            path: this.$router.history.current.path,
            systemCode: this.$router.history.current.meta.systemId,
          };
          sessionStorage.setItem(
            "locationSystem",
            JSON.stringify(locationSystem)
          );
          setTimeout(() => {
            window.top.postMessage(
              {
                event: "locationSystem", // 约定的消息事件
                args: {
                  path: url, //路径
                  systemCode: "dbjygl", //子系统编码
                }, // 参数
              },
              "*"
            );
          });
        }

        if(url) {
          this.$router.push({
            path: url,
            query: { bizId: bizId, code: bizId, textBox: activityClassify == 2 ? '活动' : '会议', title: title },
          });
        }
      }
    },
  },
};
</script>

<style lang="scss" scoped>
.register-box {
  padding: 20px;

  .titleBox {
    margin-top: 20px;
    font-weight: 700;
    padding-left: 15px;
    border-left: 4px solid red;
  }

  .register-fon {
    display: flex;
    justify-content: flex-start;
    flex-wrap: wrap;

    .register-item {
      cursor: pointer;
      margin-top: 20px;
      margin-left: 20px;
      border: 1px solid #ccc;
      height: 80px;
      width: 216px;
      padding: 0 10px;
      font-weight: 700;
      border-radius: 6px;
      // line-height:80px;
      display: flex;

      .img {
        width: 60px;
        height: 60px;
        line-height: 75px;

        img {
          width: 60px;
          height: 60px;
        }
      }
    }
  }
}

.disableItem {
  background-color: #c0c4cc;
  filter: grayscale(85%) saturate(80%);
}
</style>
