<template>
  <div>
    <a-modal
      :title="titleBox"
      :visible="isCondition"
      width="60%"
      height="300"
      @ok="handleOk"
      @cancel="handleCancel"
      cancelText="关闭"
    >
      <a-row>
        <a-col :span="8">
          <span class="title">活动类型:</span>
          {{ conferenceData.activityTypeName }}
        </a-col>
        <a-col :span="8">
          <span class="title">活动性质:</span>
          {{ conferenceData.activityNatureName }}
        </a-col>
        <a-col :span="8">
          <span class="title">活动名称:</span>
          {{ conferenceData.activityName }}
        </a-col>
      </a-row>
      <a-row>
        <a-col :span="8">
          <span class="title">开始时间:</span>
          {{ conferenceData.startTime }}
        </a-col>
        <a-col :span="8">
          <span class="title">结束时间:</span>
          {{ conferenceData.endTime }}
        </a-col>
        <a-col :span="8">
          <span class="title">活动编号:</span>
          {{ conferenceData.activityNo }}
        </a-col>
      </a-row>
      <div class="conference">
        <a-row>
          <a-col>
            <a-form ref="form" :form="queryForm" layout="inline">
              <!-- <a-form-model-item label="提出人" >
                 <a-input> </a-input>
              </a-form-model-item>-->
              <a-form-model-item>
                <!-- <a-button type="primary" @click="acquireData">查询</a-button> -->
                <a-button
                  :disabled="showDisabled"
                  type="primary"
                  @click="addData"
                  >新增</a-button
                >
                <a-button
                  :disabled="showDisabled"
                  style="margin-left: 10px"
                  type="primary"
                  @click="deleteData"
                  >删除</a-button
                >
                <a-button
                  :disabled="showDisabled"
                  style="margin-left: 10px"
                  type="primary"
                  @click="download"
                  >下载模板</a-button
                >
                <a-upload
                  name="file"
                  accept=".xls"
                  :multiple="true"
                  :file-list="fileList"
                  @change="downImport"
                  :before-upload="drUpload"
                >
                  <a-button
                    :disabled="showDisabled"
                    type="primary"
                    style="margin-left: 10px"
                    >导入</a-button
                  >
                </a-upload>
              </a-form-model-item>
            </a-form>
          </a-col>
        </a-row>
      </div>

      <a-table
        :columns="columns"
        :dataSource="dataSource"
        rowKey="id"
        :pagination="pagination"
        :row-selection="{
          selectedRowKeys: selectedRowKeys,
          onSelect: onSelectData,
          onChange: onSelectChange,
          onSelectAll: onSelectAll,
        }"
      ></a-table>
      <template slot="footer">
        <a-button @click="handleCancel">关闭</a-button>
      </template>
    </a-modal>

    <!-- 新增编辑 -->
    <a-modal
      :title="titleData"
      :visible="showVisible"
      @ok="showHandleOk"
      @cancel="showHandleCancel"
      width="40%"
      height="200"
    >
      <a-form-model
        ref="form"
        :form="queryForm"
        layout="inline"
        :label-col="{ span: 8 }"
        :wrapper-col="{ span: 10 }"
      >
        <a-row>
          <a-col>
            <a-col :span="10">
              <a-form-model-item label="分节名称">
                <a-select
                  allow-clear
                  style="width: 160px"
                  v-model="queryForm.activitySection.id"
                >
                  <a-select-option
                    v-for="item in activitySections"
                    :key="item.id"
                    :value="item.id"
                    >{{ item.sectionName }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="10">
              <a-form-model-item label="发言人">
                <a-select
                  show-search
                  :filter-option="filterOption"
                  option-filter-prop="children"
                  class="search-select"
                  v-model="queryForm.activityInviteRange.userId"
                  allow-clear
                  style="width: 160px"
                >
                  <a-select-option
                    v-for="item in dataSourceSon"
                    :key="item.userId"
                    :value="item.userId"
                    >{{ item.userName }}</a-select-option
                  >
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-col>
        </a-row>
      </a-form-model>
      <a-form-model-item style="margin-left: 6px" label="发言内容">
        <a-textarea
          v-model="queryForm.content"
          rows="6"
          allow-clear
        ></a-textarea>
      </a-form-model-item>
    </a-modal>
  </div>
</template>

<script>
import { instance_1 } from "@/api/axiosRq";
import { myPagination } from "@/mixins/pagination.js";
import { getActivityNatureList } from "@/api/registrationMage/tableIng.js";
export default {
  mixins: [myPagination],
  props: {
    titleBox: {
      type: String,
      default: "",
    },
    showDisabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      userName: "",
      iShow: false,
      fileList: [],
      selectedRowKeys: [],
      isRecord: {},
      ruleForm: {},
      queryForm: {
        id: null,
        activityId: null,

        activitySection: {
          id: null,
        },
        activityInviteRange: {
          userId: null,
        },
        content: null,
      },
      titleData: "",
      showVisible: false,
      isCondition: false,
      activitySections: [],
      attendStates: [],
      orgs: [],
      conferenceData: {},
      columns: [
        {
          title: "分节名称",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.activitySection.sectionName || "/";
          },
        },
        {
          title: "发言人",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.activityInviteRange.userName || "/";
          },
        },
        {
          title: "发言内容",
          ellipsis: true,
          customRender: (text, record, index) => {
            return record.content || "/";
          },
        },
        {
          fixed: "right",
          title: "操作",
          align: "center",
          width: 180,
          ellipsis: true,
          customRender: (text, record, index) => {
            let h = this.$createElement;
            let permission = this.showDisabled;
            return h("div", [
              h(
                "span",
                {
                  attrs: {
                    type: "text",
                  },
                  style: {
                    display: permission ? "none" : "inline-block",
                    cursor: "pointer",
                    marginLeft: "14px",
                    color: "#DB3046",
                  },
                  on: {
                    click: () => {
                      this.editData(record);
                    },
                  },
                },
                "修改"
              ),
            ]);
          },
        },
      ],
      dataSource: [],
      dataSourceSon: [],
      activityId: "",
      activitySections: [],
      rules: {
        title: [
          { required: true, message: "分节名称不能为空", trigger: "blur" },
        ],
        title: [{ required: true, message: "发言人不能为空", trigger: "blur" }],
        title: [
          { required: true, message: "发言内容不能为空", trigger: "blur" },
        ],
      },
    };
  },
  watch: {
    isVisible(newVal, old) {
      if (newVal) {
        // this.fetchData6()
      }
    },
  },
  methods: {
    filterOption(input, option) {
      //  下拉框搜索
      return (
        option.componentOptions.children[0].text
          .toLowerCase()
          .indexOf(input.toLowerCase()) >= 0
      );
    },
    showHandleOk() {
      if (this.titleData == "编辑发言情况") {
        instance_1({
          url: "/activitySpeak/updateById",
          method: "post",
          data: this.queryForm,
        }).then((res) => {
          if (res.status == "200") {
            this.showVisible = false;
            this.$baseMessage("操作成功", "success");
            this.$emit("handleClearId");
            this.fetchData6();
          } else {
            this.$baseMessage("编辑失败", "error");
          }
        });
      } else {
        instance_1({
          url: "/activitySpeak/create",
          method: "post",
          data: this.queryForm,
        }).then((res) => {
          if (res.status == "200") {
            this.showVisible = false;
            this.$emit("handleClearId");
            this.$baseMessage("操作成功", "success");
            this.fetchData6();
          } else {
            this.$baseMessage("新增失败", "error");
          }
        });
      }
    },
    //  全选
    onSelectAll(state, selectedRows, data, DA) {
      this.iShow = state;
    },
    // 选择
    onSelectChange(selectedRowKeys, selectedRows) {
      this.RowKeys = selectedRowKeys;
      this.selectedRowKeys = selectedRowKeys;
      this.selectedRows = selectedRows;
    },
    onSelectData(data, state) {
      this.iShow = state;
    },
    showHandleCancel() {
      this.activitySections = [];
      this.queryForm.activityInviteRange.userId = "";
      this.showVisible = false;
    },
    handleOk() {},
    handleCancel() {
      this.isCondition = false;
    },
    editData(record) {
      this.queryForm.id = record.id;
      instance_1({
        url: "/activityAttend/getBaseInfo",
        method: "get",
        params: { activityId: this.queryForm.activityId },
      }).then((res) => {
        if (res.data.code == "0000") {
          let { activitySections, attendStates, orgs } = res.data.data;
          this.activitySections = activitySections.splice(
            1,
            activitySections.length
          );
        }
      });
      instance_1({
        url: "/activitySpeak/getById",
        method: "get",
        params: { id: record.id },
      }).then((res) => {
        let { content, activitySection, activityInviteRange } = res.data;
        this.queryForm.activityInviteRange.userId = activityInviteRange.userId;
        this.queryForm.activitySection.id = activitySection.id;
        this.queryForm.content = content;
        this.showVisible = true;
      });

      this.titleData = "编辑发言情况";
    },
    //导入登记表文件
    downImport({ file, fileList }) {
      let formData = new FormData();
      formData.append("file", file);
      let activityId = this.queryForm.activityId;
      instance_1({
        url: `/activitySpeak/uploadActivitySpeak?activityId=${activityId}`,
        method: "post",
        headers: {
          "Content-type": "multipart/form-data",
        },
        data: formData,
      }).then((res) => {
        if (res.data.code == "0000") {
          this.$baseMessage(res.data.data || "操作成功", "success");
          this.isCondition = false;
          this.fetchData6();
          this.$emit("handleClearId");
        } else {
          this.$message.error(res.data.msg);
        }
      });
    },
    // 导入限制 直接return
    drUpload(file, fileList) {
      const isLt20M = file.size / 1024 / 1024 < 20;
      if (!isLt20M) {
        this.$message.error("上传文件大小不能超过 20M!");
      }
      var reg = /(xls)$/i;
      const isPDF = reg.test(file.name);
      if (!isPDF) {
        this.$message.error("只能选择后缀名为.xls的文件");
      }
      if (isLt20M && isPDF) return false;
    },

    acquireData() {},
    download() {
      instance_1({
        url: "/activitySpeak/download",
        method: "get",
        responseType: "blob",
      }).then((res) => {
        let a = window.document.createElement("a");
        res = URL.createObjectURL(res.data);
        a.href = res;
        a.download = `发言情况导入模板.xls`;
        window.document.body.appendChild(a);
        a.click();
        window.document.body.removeChild(a);
      });
    },
    deleteData() {
      if (this.iShow) {
        this.$baseConfirm(
          "关联数据也将一并删除，你确定要删除选中项吗",
          null,
          () => {
            instance_1({
              url: "/activitySpeak/deleteByIds",
              method: "post",
              data: this.RowKeys,
            }).then((res) => {
              if (res.data.code == "0000") {
                this.$baseMessage(res.data.msg, "success");
                this.fetchData6();
                this.$emit("handleClearId");
              }
            });
          }
        );
      } else {
        this.$baseMessage("未选中任何行", "error");
      }
    },
    addData() {
      instance_1({
        url: "/activityAttend/getBaseInfo",
        method: "get",
        params: { activityId: this.queryForm.activityId },
      }).then((res) => {
        if (res.data.code == "0000") {
          let { activitySections, attendStates, orgs } = res.data.data;
          this.activitySections = activitySections.splice(
            1,
            activitySections.length
          );
        }
      });
      this.titleData = "新增发言情况";
      this.showVisible = true;
    },
    fetchData1(id, conferenceData, isRecord) {
      this.queryForm.activityId = id;
      this.conferenceData = conferenceData;

      getActivityNatureList().then((res) => {
        if (res.data.code == "0000") {
          res.data.data.forEach((element) => {
            if (
              this.conferenceData.activityNatureDm == element.activityNatureDm
            ) {
              this.conferenceData.activityNatureName =
                element.activityNatureName;
            }
          });
        }
      });
      // this.dataSource = isRecord.attendMembers
      this.dataSourceSon = isRecord.attendMembers;
      // let dataSourceaa = isRecord.attendMembers
      // dataSourceaa.forEach(el => {
      //   {
      //     this.dataSourceSon = el
      //   }
      // })
      this.fetchData6();
    },
    fetchData6() {
      console.log(this.isRecord, "isRecordisRecordisRecord");
      instance_1({
        url: "/activitySpeak/findAll",
        method: "get",
        params: { activityId: this.queryForm.activityId },
      }).then((res) => {
        if (res.status == "200") {
          this.dataSource = res.data;
          //  this.pagination.total=res.data.length
        }
      });
      this.isCondition = true;
    },
  },
};
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  @include add-size($font_size_16);
  font-family: pingFang-M;
}
.conference {
  margin: 20px 0;
}
</style>
