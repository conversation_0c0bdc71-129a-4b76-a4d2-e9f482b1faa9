<template>
  <div>
    <a-modal title="调整代表团" :visible="isShow" :confirm-loading="confirmLoading" width="50%" @cancel="handleCancel">
      <!-- <div style="margin-bottom: 20px">
        <a-steps :current="1">
          <a-step title="登记" />
          <a-step title="初审" />
          <a-step title="终审" />
          <a-step title="完成修改" />
        </a-steps>
      </div> -->
      <div>
        <a-form-model :model="form" :label-col="labelCol" :wrapper-col="wrapperCol">
          <a-row>
            <a-col :span="24">
              <a-form-model-item label="代表团成员" :label-col="{ span: 3 }" :wrapper-col="{ span: 20 }">
                <a-textarea v-model="desc" disabled :rows="4" />
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="现属代表团">
                <a-select v-model="dbtDm" disabled>
                  <a-select-option
                    v-for="item in rawDelegationList"
                    :key="item.dbtDm"
                    :label="item.dbtmc"
                    :value="item.dbtDm"
                  >{{ item.dbtmc }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
            <a-col :span="12">
              <a-form-model-item label="调至">
                <a-select v-model="toDbtDm" :disabled="oper == constat.UPDATE">
                  <a-select-option
                    v-for="item in rawDelegationList"
                    :key="item.dbtDm"
                    :label="item.dbtmc"
                    :value="item.dbtDm"
                  >{{ item.dbtmc }}</a-select-option>
                </a-select>
              </a-form-model-item>
            </a-col>
          </a-row>
        </a-form-model>
      </div>
      <template slot="footer" v-if="LookSee != 'LookSee'">
        <a-button type="primary" v-show="oper == constat.ADD" @click="handleCreate">保存并送审</a-button>
        <a-button type="primary" v-show="oper == constat.UPDATE" @click="lczzjl">查询进度</a-button>
        <a-button type="primary" v-show="oper == constat.UPDATE" @click="handleAudit">送审</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </template>
      <template slot="footer" v-if="LookSee == 'LookSee'">
        <a-button type="primary" v-show="oper == constat.UPDATE" @click="lczzjl">查询进度</a-button>
        <a-button @click="handleCancel">取消</a-button>
      </template>
    </a-modal>

    <!-- 流程弹出页 -->
    <submitForCensorship
      ref="submitForCensorship"
      :procInstId="procInstId"
      @complete="handleComplete"
      :ids="ids"
    ></submitForCensorship>
    <lczzjl ref="lczzjl" :procInstId="procInstId"></lczzjl>
  </div>
</template>
<script>
import lczzjl from '@/views/common/lczzjl'
import {findUserManageDbtList} from "@/api/dmcs";
import { adjustingDelegationCreate, findAdjustingDelegation, adjustingDelegationComplete } from "@/api/delegation";
import constat from "@/utils/constat"
import submitForCensorship from '@/views/common/submitForCensorship';
export default {
  props: {
    members: {

      type: Array,
      default() {
        return [];
      },
    },
    isShow: {
      type: Boolean,
      default: false,
    },
    jcDm: {
      type: String,
      default: ''
    },
    id: {
      type: String,
      default: ''
    },
  },
  components: { submitForCensorship, lczzjl },
  data() {
    return {
      LookSee: "",
      visible: false,
      confirmLoading: false,
      labelCol: { span: 6 },
      wrapperCol: { span: 16 },
      form: {
        name: '',
        region: undefined,
        date1: undefined,
        delivery: false,
        type: [],
        resource: '',
        desc: '',
      },
      desc: '',
      dbtDm: '',
      toDbtDm: '',
      rawDelegationList: [],
      constat,
      oper: constat.ADD,
      procInstId: '',
      ids: []
    };
  },
  created() {
    this.getAllDelegation()
  },
  watch: {
    members: {
      handler() {
        (this.desc = ""),
          this.initMemberData()
        this.oper = constat.ADD
      },
    },
    id(newVal) {
      //加载调整人的名称
      console.log(newVal)
      findAdjustingDelegation({ id: newVal }).then(res => {
        this.desc = ""
        let members = res.data.items
        this.procInstId = res.data.procInstId
        members.forEach((item) => {
          this.desc += item.userName + "; ";
        });
        this.dbtDm = members[0].ysdbtDm
        this.toDbtDm = members[0].xsdbtDm
        this.procInstId = res.data.procInstId
        this.ids.push(res.data.id)
        this.oper = constat.UPDATE
      })
    }
  },
  methods: {
    handleOk(){
      this.isShow = false
    },
    //查询进度
    lczzjl() {
      this.$refs.lczzjl.visible = true
      this.$refs.lczzjl.id = this.procInstId
    },
    showModal() {
      this.visible = true
    },
    handleCreate(e) {
      this.confirmLoading = true
      let items = []
      this.members.forEach(element => {
        let item = {}
        item.dbId = element.employeeId
        item.userName = element.employeeName
        item.xsdbtDm = this.toDbtDm
        item.ysdbtDm = this.dbtDm
        items.push(item)
      });
      let data = {
        jcDm: this.jcDm
      }
      data.items = items
      this.adjustingDelegationCreate(data)
      setTimeout(() => {
        this.visible = false;
        this.confirmLoading = false;
      }, 5000);
    },
    handleCancel(e) {
      console.log('Clicked cancel button');
      this.$emit('onClose')
    },
    initMemberData() {
      this.members.forEach((item) => {
        this.desc += item.employeeName + "; ";
      });
      this.dbtDm = this.members[0].organizationId
    },
    getAllDelegation() {
      findUserManageDbtList().then(response => {
        this.rawDelegationList = response.data
      })
    },
    adjustingDelegationCreate(data) {
      adjustingDelegationCreate(data).then(response => {
        this.$baseMessage("操作成功", "info");
        this.procInstId = response.data.procInstId
        this.ids.push(response.data.id)
        this.handleAudit()
        this.handleCancel()
        this.confirmLoading = false;
      })
    },
    handleAudit() {
      //弹出审核框
      this.$refs.submitForCensorship.visible = true;
    },
    handleComplete(data) {
      adjustingDelegationComplete(data).then(res => {
        //如果成功
        console.log(res, '00000000');
        if (res.code == '0000') {
          this.isShowIn = false
          this.$emit('handleClearId')
          this.ids = []
          this.$refs.submitForCensorship.successComplete()
          this.$message.success(res.msg)
          this.$router.push('/delegate/Handling')
        } else {
          this.$message.error(res.msg)
        }
      })
    },
  },
};
</script>
