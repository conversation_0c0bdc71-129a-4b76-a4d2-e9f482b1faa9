<template>
  <!-- 录入重复提示 -->
  <div>
    <a-modal
      title="录入重复检测"
      :visible="visible"
      :confirm-loading="confirmLoading"
      @ok="handleOk"
      @cancel="visible = false"
      width="60%"
      okText="已明确，确定继续录入"
      cancelText="取消录入"
    >
    <a-card>
      <span style="color: red">可能存在重复录入的情况，请确认是否继续操作！</span>
    </a-card>
    <a-table :columns="columns" :data-source="dataList">
    <p slot="expandedRowRender" slot-scope="record" style="margin: 0">
      <a-table :columns="subColumns" :data-source="record.members">
      </a-table>
    </p>
  </a-table>
    </a-modal>
  </div>
</template>

<script>
import {
  checkConflict,
} from "@/api/registrationMage/tableIng"


const columns = [
  { title: '活动名称',
    dataIndex: 'activity.activityName',
    ellipsis: true 
  },
  { title: '开始时间',
    dataIndex: 'activity.startTime',
    width: 150,
    ellipsis: true 
  },
  { title: '结束时间',
    dataIndex: 'activity.endTime',
    width: 150,
    ellipsis: true 
  },
  { title: '状态',
    dataIndex: 'activity.currStateName',
    width: 100,
    ellipsis: true 
  },
  { title: '录入人',
    dataIndex: 'activity.creatorName',
    width: 100,
    ellipsis: true
  },
  { title: '录入时间',
    dataIndex: 'activity.createTime',
    width: 150,
    ellipsis: true
  },
];
const subColumns = [
  { title: '所属代表团',
    dataIndex: 'orgName',
    ellipsis: true
  }, 
  { title: '代表姓名',
    dataIndex: 'userName',
    ellipsis: true
  }
]
export default {
  data() {
    return {
      columns,
      subColumns,
      dataList: [],
      ModalText: "Content of the modal",
      visible: false,
      confirmLoading: false,
    };
  },
  methods: {
    checkActivityConflict(form) {
      this.confirmLoading = true
      checkConflict(form).then(res => {
        this.confirmLoading = false
        if(res.data.code == '0000') {
          this.$emit("submit")
        }else {
          this.visible = true
          this.dataList = res.data.data
        }
      })
    },
    handleOk() {
      this.visible = false
      this.$emit("submit")
    }
  }
};
</script>

<style>
</style>